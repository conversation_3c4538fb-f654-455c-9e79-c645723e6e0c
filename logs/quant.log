2025-07-15 14:10:57,837 [INFO] quant.monitoring.add_alert_rule:276 - 添加告警规则: high_cpu_usage
2025-07-15 14:10:57,837 [INFO] quant.monitoring.add_alert_rule:276 - 添加告警规则: high_memory_usage
2025-07-15 14:10:57,837 [INFO] quant.monitoring.add_alert_rule:276 - 添加告警规则: low_success_rate
2025-07-15 14:10:59,756 [DEBUG] quant.text_analyzer._load_stopwords:50 - 加载停用词: 1952 个
2025-07-15 14:10:59,756 [WARNING] quant.text_analyzer.__init__:39 - jieba不可用，使用基础文本分析
2025-07-15 14:10:59,757 [INFO] quant.proxy.__init__:55 - 代理管理器初始化完成
2025-07-15 14:10:59,766 [ERROR] quant.database._create_engine:117 - Failed to create database engine: No module named 'pymysql'
2025-07-15 14:11:09,137 [INFO] quant.monitoring.add_alert_rule:276 - 添加告警规则: high_cpu_usage
2025-07-15 14:11:09,137 [INFO] quant.monitoring.add_alert_rule:276 - 添加告警规则: high_memory_usage
2025-07-15 14:11:09,137 [INFO] quant.monitoring.add_alert_rule:276 - 添加告警规则: low_success_rate
2025-07-15 14:11:09,367 [DEBUG] quant.text_analyzer._load_stopwords:50 - 加载停用词: 1952 个
2025-07-15 14:11:09,367 [WARNING] quant.text_analyzer.__init__:39 - jieba不可用，使用基础文本分析
2025-07-15 14:11:09,367 [INFO] quant.proxy.__init__:55 - 代理管理器初始化完成
2025-07-15 14:11:09,381 [INFO] quant.database._create_engine:114 - Database engine created successfully for 127.0.0.1:3308/blackbear
2025-07-15 14:11:09,419 [INFO] quant.akshare.__init__:125 - AkShare数据源初始化完成
2025-07-15 14:11:09,419 [INFO] quant.akshare.__init__:461 - akshare量化数据源初始化
2025-07-15 14:11:10,074 [INFO] quant.baostock.login:58 - BaoStock登录成功
2025-07-15 14:11:10,074 [INFO] quant.baostock.__init__:396 - BaoStock量化数据源初始化
2025-07-15 14:11:10,076 [INFO] quant.settings._validate_settings:114 - Settings validation passed for environment: development
2025-07-15 14:11:10,076 [INFO] quant.settings.get_settings:154 - Global settings initialized
2025-07-15 14:11:10,076 [INFO] quant.quant_all_stocks.main:228 - 同步所有股票code
2025-07-15 14:11:10,076 [ERROR] quant.quant_all_stocks.sync_stock_dict:191 - 'Akshare' object has no attribute 'stock_info_a_code_name'
2025-07-15 14:11:10,312 [INFO] quant.fetch.fetch_all_stock:100 - 获取所有股票的code，总计5507
2025-07-15 14:11:10,314 [ERROR] quant.quant_all_stocks.main:231 - 同步所有股票code失败: local variable 'stock_info_a_code_name_df' referenced before assignment
2025-07-15 14:11:10,472 [INFO] quant.fetch.fetch_all_stock:100 - 获取所有股票的code，总计5507
2025-07-15 14:11:10,526 [INFO] quant.quant_all_stocks.method1:125 - Method1 processing: {'stock_code': '000001', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,526 [DEBUG] quant.akshare.get_stock_history:156 - 获取股票 000001 历史数据: 20250312 至 20240601
2025-07-15 14:11:10,528 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000002', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,528 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000002 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,528 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000002 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,529 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000004', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,529 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000004 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,529 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000004 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,530 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000005', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,530 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000005 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,530 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000005 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,530 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000006', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,530 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000006 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,530 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000006 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,531 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000007', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,531 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000007 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,531 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000007 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,531 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000008', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,531 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000008 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,531 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000008 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,532 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000009', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,532 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000009 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,532 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000009 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,532 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000010', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,532 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000010 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,532 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000010 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,533 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000011', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,533 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000011 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,533 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000011 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,533 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000012', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,533 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000012 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,533 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000012 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,534 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000014', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,534 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000014 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,534 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000014 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,534 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000016', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,534 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000016 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,534 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000016 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,535 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000017', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,535 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000017 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,535 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000017 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,535 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000019', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,536 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000019 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,536 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000019 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,536 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000020', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,536 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000020 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,536 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000020 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,536 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000021', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,537 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000021 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,537 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000021 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,537 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000023', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,537 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000023 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,537 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000023 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,537 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000025', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,538 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000025 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,538 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000025 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,538 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000026', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,538 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000026 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,538 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000026 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,539 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000027', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,539 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000027 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,539 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000027 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,539 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000028', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,539 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000028 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,539 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000028 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,540 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000029', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,540 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000029 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,540 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000029 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,540 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000030', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,541 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000030 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,541 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000030 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,541 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000031', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,541 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000031 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,541 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000031 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,542 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000032', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,542 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000032 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,542 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000032 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,542 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000034', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,542 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000034 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,543 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000034 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,543 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000035', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,543 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000035 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,543 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000035 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,544 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000036', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,544 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000036 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,544 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000036 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,545 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000037', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,545 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000037 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,545 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000037 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,545 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000039', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,545 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000039 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,545 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000039 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,546 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000040', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,546 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000040 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,546 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000040 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,546 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000042', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,546 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000042 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,547 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000042 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,547 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000045', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,547 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000045 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,547 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000045 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,548 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000046', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,548 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000046 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,548 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000046 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,548 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000048', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,548 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000048 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,548 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000048 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,549 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000049', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,549 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000049 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,549 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000049 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,549 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000050', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,550 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000050 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,550 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000050 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,550 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000055', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,550 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000055 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,550 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000055 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,551 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000056', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,551 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000056 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,551 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000056 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,551 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000058', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,551 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000058 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,551 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000058 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,552 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000059', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,552 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000059 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,552 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000059 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,552 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000060', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,553 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000060 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,553 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000060 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,553 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000061', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,553 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000061 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,553 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000061 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,554 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000062', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,554 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000062 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,554 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000062 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,554 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000063', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,554 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000063 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,554 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000063 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,555 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000065', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,555 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000065 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,555 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000065 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,555 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000066', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,556 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000066 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,556 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000066 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,556 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000068', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,556 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000068 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,556 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000068 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,557 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000069', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,557 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000069 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,557 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000069 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,557 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000070', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,558 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000070 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,558 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000070 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,558 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000078', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,558 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000078 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,558 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000078 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,559 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000088', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,559 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000088 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,559 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000088 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,559 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000089', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,559 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000089 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,560 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000089 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,560 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000090', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,560 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000090 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,560 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000090 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,561 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000096', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,561 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000096 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,561 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000096 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,561 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000099', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,561 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000099 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,561 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000099 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,568 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000100', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,568 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000100 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,568 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000100 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,575 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000151', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,575 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000151 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,575 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000151 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,578 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000153', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,578 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000153 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,578 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000153 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,583 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000155', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,583 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000155 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,583 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000155 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,589 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000156', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,590 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000156 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,590 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000156 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,591 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000157', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,591 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000157 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,591 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000157 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,593 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000158', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,593 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000158 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,593 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000158 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,599 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000159', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,599 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000159 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,599 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000159 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,602 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000166', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,602 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000166 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,602 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000166 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,612 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000301', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,612 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000301 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,613 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000301 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,622 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000333', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,622 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000333 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,622 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000333 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,622 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000338', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,622 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000338 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,622 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000338 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,623 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000400', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,623 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000400 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,623 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000400 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,623 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000401', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,624 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000401 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,624 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000401 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,624 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000402', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,624 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000402 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,624 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000402 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,625 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000403', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,625 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000403 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,625 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000403 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,625 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000404', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,625 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000404 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,626 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000404 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,626 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000407', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,626 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000407 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,626 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000407 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,627 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000408', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,627 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000408 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,627 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000408 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,627 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000409', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,627 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000409 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,627 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000409 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,628 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000410', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,628 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000410 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,628 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000410 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,628 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000411', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,628 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000411 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,628 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000411 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,629 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000413', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,629 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000413 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,629 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000413 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,629 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000415', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,629 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000415 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,629 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000415 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,630 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000416', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,630 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000416 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,630 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000416 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,630 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000417', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,630 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000417 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,630 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000417 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,631 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000419', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,631 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000419 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,631 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000419 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,631 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000420', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,631 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000420 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,632 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000420 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,632 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000421', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,632 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000421 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,632 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000421 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,633 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000422', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,633 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000422 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,633 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000422 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,633 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000423', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,633 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000423 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,633 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000423 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,634 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000425', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,634 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000425 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,634 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000425 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,634 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000426', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,634 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000426 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,634 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000426 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,635 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000428', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,635 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000428 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,635 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000428 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,635 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000429', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,636 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000429 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,636 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000429 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,636 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000430', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,636 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000430 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,636 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000430 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,637 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000488', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,637 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000488 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,637 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000488 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,638 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000498', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,639 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000498 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,639 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000498 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,639 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000501', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,639 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000501 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,639 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000501 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,640 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000503', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,640 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000503 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,640 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000503 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,642 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000504', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,642 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000504 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,642 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000504 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,651 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000505', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,652 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000505 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,652 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000505 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,655 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000506', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 0}
2025-07-15 14:11:10,655 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000506 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,655 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000506 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,656 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000002', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,656 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000002 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,656 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000002 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,657 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000004', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,657 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000004 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,657 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000004 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,657 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000005', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,657 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000005 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,657 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000005 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,658 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000006', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,658 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000006 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,658 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000006 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,659 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000007', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,659 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000007 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,659 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000007 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,659 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000008', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,659 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000008 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,659 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000008 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,660 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000009', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,660 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000009 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,660 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000009 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,660 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000010', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,660 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000010 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,660 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000010 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,661 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000011', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,661 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000011 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,661 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000011 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,661 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000012', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,661 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000012 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,661 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000012 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,662 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000014', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,662 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000014 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,662 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000014 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,662 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000016', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,662 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000016 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,663 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000016 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,663 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000017', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,663 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000017 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,663 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000017 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,664 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000019', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,664 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000019 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,664 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000019 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,664 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000020', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,664 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000020 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,664 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000020 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,665 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000021', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,665 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000021 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,665 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000021 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,665 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000023', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,665 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000023 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,665 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000023 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,666 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000025', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,666 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000025 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,666 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000025 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,666 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000026', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,667 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000026 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,667 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000026 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,668 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000027', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,668 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000027 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,668 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000027 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,668 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000028', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,668 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000028 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,669 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000028 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,670 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000029', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,670 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000029 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,670 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000029 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,670 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000030', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,670 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000030 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,670 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000030 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,671 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000031', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,671 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000031 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,671 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000031 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,671 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000032', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,672 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000032 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,672 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000032 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,672 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000034', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,672 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000034 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,672 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000034 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,673 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000035', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,673 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000035 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,673 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000035 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,673 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000036', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,673 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000036 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,674 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000036 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,674 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000037', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,674 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000037 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,674 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000037 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,675 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000039', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,676 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000039 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,676 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000039 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,676 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000040', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,676 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000040 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,676 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000040 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,677 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000042', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,677 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000042 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,677 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000042 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,677 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000045', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,677 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000045 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,677 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000045 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,678 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000046', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,678 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000046 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,678 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000046 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,678 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000048', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,678 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000048 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,679 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000048 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,679 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000049', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,679 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000049 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,679 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000049 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,680 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000050', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,680 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000050 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,680 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000050 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,680 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000055', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,680 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000055 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,680 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000055 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,681 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000056', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,681 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000056 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,681 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000056 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,681 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000058', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,682 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000058 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,682 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000058 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,682 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000059', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,682 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000059 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,682 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000059 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,683 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000060', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,683 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000060 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,683 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000060 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,683 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000061', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,683 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000061 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,683 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000061 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,684 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000062', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,684 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000062 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,684 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000062 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,685 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000063', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,685 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000063 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,685 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000063 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,686 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000065', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,686 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000065 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,686 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000065 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,686 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000066', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,687 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000066 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,687 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000066 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,687 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000068', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,687 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000068 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,687 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000068 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,688 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000069', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,688 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000069 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,688 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000069 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,688 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000070', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,688 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000070 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,688 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000070 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,689 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000078', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,689 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000078 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,689 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000078 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,690 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000088', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,690 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000088 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,690 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000088 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,690 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000089', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,690 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000089 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,690 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000089 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,691 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000090', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,691 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000090 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,691 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000090 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,691 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000096', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,691 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000096 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,691 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000096 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,692 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000099', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,692 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000099 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,692 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000099 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,692 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000100', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,693 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000100 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,693 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000100 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,693 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000151', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,693 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000151 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,693 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000151 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,694 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000153', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,694 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000153 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,694 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000153 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,694 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000155', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,694 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000155 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,694 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000155 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,695 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000156', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,695 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000156 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,695 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000156 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,695 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000157', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,695 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000157 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,695 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000157 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,696 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000158', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,696 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000158 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,696 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000158 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,697 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000159', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,697 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000159 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,697 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000159 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,697 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000166', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,698 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000166 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,698 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000166 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,698 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000301', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,698 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000301 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,698 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000301 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,699 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000333', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,699 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000333 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,699 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000333 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,699 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000338', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,699 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000338 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,699 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000338 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,700 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000400', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,700 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000400 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,700 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000400 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,700 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000401', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,700 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000401 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,700 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000401 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,701 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000402', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,701 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000402 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,701 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000402 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,701 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000403', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,702 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000403 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,702 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000403 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,702 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000404', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,702 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000404 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,702 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000404 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,703 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000407', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,703 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000407 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,703 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000407 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,703 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000408', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,703 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000408 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,703 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000408 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,704 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000409', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,704 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000409 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,704 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000409 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,704 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000410', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,704 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000410 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,705 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000410 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,705 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000411', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,705 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000411 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,705 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000411 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,706 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000413', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,706 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000413 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,706 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000413 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,706 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000415', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,706 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000415 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,706 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000415 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,707 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000416', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,707 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000416 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,707 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000416 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,707 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000417', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,707 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000417 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,707 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000417 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,708 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000419', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,708 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000419 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,708 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000419 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,709 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000420', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,709 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000420 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,709 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000420 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,709 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000421', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,709 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000421 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,709 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000421 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,710 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000422', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,710 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000422 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,710 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000422 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,710 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000423', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,710 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000423 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,710 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000423 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,711 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000425', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,711 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000425 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,711 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000425 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,711 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000426', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,712 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000426 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,712 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000426 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,712 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000428', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,712 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000428 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,712 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000428 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,713 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000429', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,713 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000429 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,713 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000429 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,713 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000430', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,713 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000430 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,713 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000430 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,714 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000488', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,714 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000488 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,714 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000488 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,714 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000498', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,714 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000498 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,715 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000498 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,715 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000501', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,715 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000501 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,715 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000501 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,716 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000503', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,716 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000503 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,716 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000503 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,716 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000504', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,716 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000504 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,716 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000504 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,717 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000505', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,717 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000505 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,717 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000505 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,717 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000506', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:10,717 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000506 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,717 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000506 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,718 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000002', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,718 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000002 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,718 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000002 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,719 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000004', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,719 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000004 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,719 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000004 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,719 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000005', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,719 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000005 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,720 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000005 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,720 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000006', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,720 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000006 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,720 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000006 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,721 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000007', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,721 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000007 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,721 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000007 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,721 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000008', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,721 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000008 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,721 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000008 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,722 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000009', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,722 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000009 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,722 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000009 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,722 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000010', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,722 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000010 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,722 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000010 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,723 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000011', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,723 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000011 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,723 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000011 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,723 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000012', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,723 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000012 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,724 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000012 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,724 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000014', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,724 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000014 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,724 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000014 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,725 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000016', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,725 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000016 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,725 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000016 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,725 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000017', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,725 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000017 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,725 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000017 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,726 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000019', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,726 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000019 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,726 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000019 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,727 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000020', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,727 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000020 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,727 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000020 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,727 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000021', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,727 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000021 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,727 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000021 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,728 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000023', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,728 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000023 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,728 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000023 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,728 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000025', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,728 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000025 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,728 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000025 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,729 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000026', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,729 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000026 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,729 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000026 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,730 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000027', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,730 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000027 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,730 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000027 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,730 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000028', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,730 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000028 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,730 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000028 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,731 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000029', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,731 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000029 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,731 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000029 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,731 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000030', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,731 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000030 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,731 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000030 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,732 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000031', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,732 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000031 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,732 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000031 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,733 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000032', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,733 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000032 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,733 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000032 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,733 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000034', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,733 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000034 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,733 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000034 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,734 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000035', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,734 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000035 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,734 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000035 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,734 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000036', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,735 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000036 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,735 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000036 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,735 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000037', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,735 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000037 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,735 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000037 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,736 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000039', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,736 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000039 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,736 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000039 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,736 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000040', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,736 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000040 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,736 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000040 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,737 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000042', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,737 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000042 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,737 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000042 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,737 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000045', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,737 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000045 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,737 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000045 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,738 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000046', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,738 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000046 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,738 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000046 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,738 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000048', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,738 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000048 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,739 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000048 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,739 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000049', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,739 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000049 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,739 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000049 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,740 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000050', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,740 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000050 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,740 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000050 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,740 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000055', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,740 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000055 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,740 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000055 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,741 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000056', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,741 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000056 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,741 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000056 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,741 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000058', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,741 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000058 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,741 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000058 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,742 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000059', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,742 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000059 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,742 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000059 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,742 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000060', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,743 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000060 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,743 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000060 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,743 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000061', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,743 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000061 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,743 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000061 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,744 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000062', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,744 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000062 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,744 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000062 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,744 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000063', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,744 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000063 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,744 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000063 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,745 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000065', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,745 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000065 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,745 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000065 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,745 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000066', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,745 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000066 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,746 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000066 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,746 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000068', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,746 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000068 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,746 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000068 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,747 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000069', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,747 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000069 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,747 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000069 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,747 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000070', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,747 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000070 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,747 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000070 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,748 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000078', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,748 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000078 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,748 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000078 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,749 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000088', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,749 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000088 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,749 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000088 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,749 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000089', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,749 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000089 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,749 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000089 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,750 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000090', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,750 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000090 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,750 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000090 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,750 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000096', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,750 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000096 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,750 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000096 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,751 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000099', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,751 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000099 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,751 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000099 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,751 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000100', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,752 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000100 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,752 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000100 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,752 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000151', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,752 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000151 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,752 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000151 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,753 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000153', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,753 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000153 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,753 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000153 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,753 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000155', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,753 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000155 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,754 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000155 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,754 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000156', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,754 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000156 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,754 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000156 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,755 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000157', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,755 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000157 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,755 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000157 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,756 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000158', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,756 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000158 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,756 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000158 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,756 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000159', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,757 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000159 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,757 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000159 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,757 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000166', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,757 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000166 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,757 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000166 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,758 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000301', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,758 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000301 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,758 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000301 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,759 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000333', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,759 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000333 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,759 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000333 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,759 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000338', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,759 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000338 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,759 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000338 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,760 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000400', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,760 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000400 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,760 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000400 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,760 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000401', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,760 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000401 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,760 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000401 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,761 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000402', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,761 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000402 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,761 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000402 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,761 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000403', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,762 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000403 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,762 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000403 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,762 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000404', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,762 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000404 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,762 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000404 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,763 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000407', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,763 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000407 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,763 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000407 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,763 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000408', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,763 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000408 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,764 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000408 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,764 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000409', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,764 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000409 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,764 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000409 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,765 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000410', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,765 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000410 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,765 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000410 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,765 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000411', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,765 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000411 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,765 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000411 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,766 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000413', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,766 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000413 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,766 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000413 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,767 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000415', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,767 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000415 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,767 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000415 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,768 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000416', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,768 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000416 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,768 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000416 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,769 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000417', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,769 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000417 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,769 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000417 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,769 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000419', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,770 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000419 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,770 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000419 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,770 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000420', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,770 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000420 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,770 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000420 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,771 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000421', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,771 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000421 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,771 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000421 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,771 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000422', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,771 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000422 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,772 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000422 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,772 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000423', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,772 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000423 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,772 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000423 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,773 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000425', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,773 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000425 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,773 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000425 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,774 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000426', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,774 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000426 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,774 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000426 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,774 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000428', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,774 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000428 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,774 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000428 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,775 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000429', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,775 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000429 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,775 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000429 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,776 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000430', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,776 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000430 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,776 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000430 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,777 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000488', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,777 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000488 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,777 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000488 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,777 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000498', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,777 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000498 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,777 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000498 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,778 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000501', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,778 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000501 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,778 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000501 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,779 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000503', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,779 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000503 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,779 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000503 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,779 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000504', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,779 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000504 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,779 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000504 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,780 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000505', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,780 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000505 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,780 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000505 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,781 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000506', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:10,781 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000506 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,781 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000506 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,781 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000002', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,782 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000002 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,782 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000002 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,782 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000004', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,783 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000004 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,783 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000004 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,783 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000005', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,783 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000005 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,783 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000005 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,784 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000006', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,784 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000006 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,784 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000006 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,785 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000007', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,785 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000007 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,785 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000007 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,785 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000008', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,785 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000008 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,785 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000008 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,786 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000009', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,786 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000009 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,786 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000009 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,787 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000010', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,787 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000010 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,787 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000010 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,788 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000011', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,788 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000011 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,788 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000011 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,789 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000012', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,789 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000012 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,789 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000012 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,790 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000014', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,790 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000014 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,790 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000014 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,790 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000016', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,791 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000016 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,791 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000016 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,791 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000017', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,791 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000017 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,791 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000017 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,792 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000019', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,792 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000019 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,792 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000019 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,792 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000020', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,793 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000020 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,793 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000020 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,793 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000021', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,793 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000021 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,793 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000021 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,794 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000023', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,794 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000023 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,794 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000023 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,794 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000025', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,794 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000025 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,795 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000025 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,795 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000026', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,795 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000026 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,795 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000026 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,796 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000027', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,796 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000027 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,796 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000027 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,796 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000028', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,796 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000028 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,796 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000028 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,797 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000029', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,797 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000029 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,797 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000029 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,797 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000030', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,797 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000030 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,797 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000030 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,798 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000031', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,798 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000031 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,798 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000031 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,798 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000032', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,798 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000032 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,799 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000032 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,799 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000034', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,799 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000034 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,799 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000034 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,800 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000035', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,800 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000035 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,800 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000035 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,800 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000036', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,800 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000036 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,800 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000036 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,801 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000037', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,801 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000037 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,801 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000037 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,801 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000039', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,801 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000039 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,801 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000039 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,802 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000040', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,802 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000040 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,802 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000040 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,802 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000042', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,802 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000042 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,802 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000042 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,803 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000045', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,803 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000045 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,803 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000045 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,803 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000046', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,804 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000046 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,804 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000046 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,804 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000048', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,804 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000048 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,804 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000048 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,805 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000049', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,805 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000049 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,805 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000049 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,805 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000050', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,805 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000050 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,805 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000050 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,806 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000055', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,806 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000055 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,806 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000055 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,806 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000056', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,806 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000056 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,807 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000056 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,807 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000058', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,807 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000058 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,807 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000058 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,807 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000059', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,808 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000059 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,808 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000059 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,808 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000060', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,808 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000060 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,808 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000060 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,809 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000061', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,809 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000061 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,809 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000061 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,809 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000062', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,809 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000062 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,809 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000062 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,810 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000063', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,810 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000063 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,810 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000063 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,810 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000065', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,810 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000065 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,810 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000065 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,811 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000066', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,811 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000066 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,811 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000066 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,811 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000068', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,811 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000068 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,811 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000068 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,812 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000069', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,812 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000069 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,812 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000069 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,812 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000070', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,813 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000070 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,813 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000070 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,813 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000078', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,813 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000078 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,813 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000078 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,814 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000088', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,814 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000088 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,814 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000088 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,814 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000089', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,814 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000089 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,814 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000089 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,815 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000090', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,815 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000090 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,815 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000090 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,815 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000096', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,815 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000096 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,815 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000096 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,816 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000099', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,816 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000099 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,816 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000099 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,816 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000100', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,816 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000100 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,817 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000100 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,817 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000151', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,817 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000151 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,817 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000151 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,818 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000153', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,818 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000153 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,818 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000153 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,818 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000155', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,818 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000155 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,818 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000155 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,819 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000156', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,819 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000156 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,819 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000156 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,819 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000157', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,819 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000157 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,819 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000157 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,820 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000158', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,820 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000158 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,820 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000158 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,820 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000159', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,821 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000159 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,821 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000159 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,821 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000166', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,821 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000166 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,821 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000166 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,822 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000301', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,822 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000301 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,822 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000301 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,822 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000333', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,822 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000333 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,822 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000333 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,823 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000338', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,823 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000338 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,823 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000338 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,823 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000400', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,823 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000400 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,824 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000400 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,824 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000401', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,824 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000401 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,824 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000401 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,825 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000402', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,825 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000402 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,825 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000402 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,825 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000403', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,825 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000403 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,825 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000403 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,826 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000404', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,826 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000404 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,826 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000404 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,826 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000407', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,826 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000407 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,826 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000407 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,827 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000408', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,827 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000408 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,827 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000408 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,827 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000409', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,828 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000409 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,828 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000409 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,828 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000410', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,828 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000410 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,828 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000410 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,829 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000411', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,829 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000411 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,829 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000411 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,829 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000413', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,829 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000413 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,829 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000413 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,830 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000415', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,830 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000415 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,830 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000415 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,830 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000416', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,830 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000416 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,830 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000416 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,831 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000417', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,831 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000417 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,831 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000417 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,831 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000419', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,831 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000419 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,832 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000419 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,832 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000420', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,832 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000420 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,832 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000420 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,832 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000421', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,833 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000421 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,833 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000421 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,833 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000422', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,833 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000422 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,833 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000422 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,834 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000423', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,834 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000423 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,834 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000423 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,834 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000425', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,834 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000425 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,834 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000425 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,835 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000426', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,835 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000426 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,835 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000426 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,835 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000428', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,835 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000428 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,835 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000428 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,836 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000429', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,836 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000429 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,836 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000429 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,836 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000430', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,837 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000430 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,837 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000430 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,837 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000488', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,837 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000488 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,837 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000488 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,838 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000498', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,838 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000498 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,838 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000498 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,838 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000501', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,838 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000501 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,838 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000501 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,839 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000503', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,839 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000503 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,839 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000503 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,839 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000504', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,839 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000504 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,840 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000504 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,840 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000505', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,840 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000505 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,840 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000505 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,841 [INFO] quant.quant_all_stocks.method2:129 - Method2 processing: {'stock_code': '000506', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:10,841 [DEBUG] quant.baostock.get_stock_history:126 - 获取股票 sz.000506 历史数据: 2025-03-12 至 2024-06-01
2025-07-15 14:11:10,841 [ERROR] quant.baostock.get_stock_history:142 - 查询股票 sz.000506 历史数据失败: 起始日期大于终止日期，请修改。
2025-07-15 14:11:10,860 [INFO] quant.quant_all_stocks.worker:139 - stock_data_queue is empty. Checking event to exit.
2025-07-15 14:11:11,004 [ERROR] quant.akshare.get_stock_history:194 - 获取股票 000001 历史数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-15 14:11:11,006 [INFO] quant.quant_all_stocks.method1:125 - Method1 processing: {'stock_code': '000001', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 1}
2025-07-15 14:11:11,006 [DEBUG] quant.akshare.get_stock_history:156 - 获取股票 000001 历史数据: 20250312 至 20240601
2025-07-15 14:11:11,088 [ERROR] quant.akshare.get_stock_history:194 - 获取股票 000001 历史数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-15 14:11:11,092 [INFO] quant.quant_all_stocks.method1:125 - Method1 processing: {'stock_code': '000001', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 2}
2025-07-15 14:11:11,092 [DEBUG] quant.akshare.get_stock_history:156 - 获取股票 000001 历史数据: 20250312 至 20240601
2025-07-15 14:11:11,198 [ERROR] quant.akshare.get_stock_history:194 - 获取股票 000001 历史数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-15 14:11:11,200 [INFO] quant.quant_all_stocks.method1:125 - Method1 processing: {'stock_code': '000001', 'start_date': '20250312', 'end_date': '20240601', 'onlyend': False, 'fail_count': 3}
2025-07-15 14:11:11,200 [DEBUG] quant.akshare.get_stock_history:156 - 获取股票 000001 历史数据: 20250312 至 20240601
2025-07-15 14:11:11,284 [ERROR] quant.akshare.get_stock_history:194 - 获取股票 000001 历史数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-15 14:11:11,287 [INFO] quant.quant_all_stocks.worker:139 - stock_data_queue is empty. Checking event to exit.
