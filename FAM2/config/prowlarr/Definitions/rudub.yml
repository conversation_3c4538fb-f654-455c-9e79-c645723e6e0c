---
id: rudub
replaces:
  - b<PERSON><PERSON><PERSON>
name: RUDUB
description: "RUDU<PERSON> (ex-BaibaKoTV) is a RUSSIAN Semi-Private Torrent Tracker for TV"
language: ru-RU
type: semi-private
encoding: windows-1251
followredirect: true
links:
  - https://jul14.rudub.shop/
legacylinks:
  - http://jun27.rudub.icu/
  - https://jun27.rudub.icu/
  - http://jun28.rudub.icu/
  - https://jun28.rudub.icu/
  - http://jun29.rudub.icu/
  - https://jun29.rudub.icu/
  - http://jun30.rudub.icu/
  - https://jun30.rudub.icu/
  - http://jul01.rudub.icu/
  - https://jul01.rudub.icu/
  - http://jul02.rudub.icu/
  - https://jul02.rudub.icu/
  - http://jul03.rudub.shop/
  - https://jul03.rudub.shop/
  - http://jul04.rudub.shop/
  - https://jul04.rudub.shop/
  - http://jul05.rudub.shop/
  - https://jul05.rudub.shop/
  - http://jul06.rudub.shop/
  - https://jul06.rudub.shop/
  - http://jul07.rudub.shop/
  - https://jul07.rudub.shop/
  - http://jul08.rudub.shop/
  - https://jul08.rudub.shop/
  - http://jul09.rudub.shop/
  - https://jul09.rudub.shop/
  - http://jul10.rudub.shop/
  - https://jul10.rudub.shop/
  - http://jul11.rudub.shop/
  - https://jul11.rudub.shop/
  - http://jul12.rudub.shop/
  - https://jul12.rudub.shop/
  - http://jul13.rudub.shop/
  - https://jul13.rudub.shop/
  - http://jul14.rudub.shop/

caps:
  categorymappings:
    - {id: 1, cat: TV, desc: "TV"}

  modes:
    search: [q]
    tv-search: [q, season, ep]
  allowrawsearch: true

settings:
  - name: username
    type: text
    label: Username
  - name: password
    type: password
    label: Password
  - name: stripcyrillic
    type: checkbox
    label: Strip Cyrillic Letters
    default: false
  - name: addrussiantotitle
    type: checkbox
    label: Add RUS to end of all titles to improve language detection by Sonarr and Radarr. Will cause English-only results to be misidentified.
    default: false
  - name: freeleech
    type: checkbox
    label: Search freeleech only
    default: false
  - name: sort
    type: select
    label: Sort requested from site
    default: 4
    options:
      4: created
      7: seeders
      5: size
      1: title
  - name: type
    type: select
    label: Order requested from site
    default: desc
    options:
      desc: desc
      asc: asc

login:
  path: takelogin.php
  method: post
  inputs:
    username: "{{ .Config.username }}"
    password: "{{ .Config.password }}"
  error:
    - selector: div.login2
  test:
    path: browse.php
    selector: a[href="logout.php"]

search:
  paths:
    # http://rudub.tv/browse.php?search=&incldead=1&sort=4&type=desc
    - path: browse.php
  inputs:
    search: "{{ .Keywords }}"
    # 0 incldead, 1 active, 2 onlydead, 3 gold, 4 seedfree
    incldead: "{{ if .Config.freeleech }}3{{ else }}0{{ end }}"
    sort: "{{ .Config.sort }}"
    type: "{{ .Config.type }}"

  keywordsfilters:
    # strip season and/or ep
    - name: re_replace
      args: ["(?i)\\b(?:[SE]\\d{1,4}){1,2}\\b\\s?", ""]

  rows:
    selector: div.card__torlist__browse_2

  fields:
    category:
      text: 1
    title:
      selector: a[href^="details.php?id="]
      filters:
        # normalize to SXXEYY format
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*[CС]езоны?.+?(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))?", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*[CС]езоны?.+?(\\d+(?:-\\d+)?)\\s*(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))\\s+из\\s*(\\w?)", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))?", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(\\d+(?:-\\d+)?)\\s*(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))\\s+из\\s*(\\w?)", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)", "S$1E$2"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*[CС]езоны?.+?(\\d+(?:-\\d+)?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "S$1E$2"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(\\d+(?:-\\d+)?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "S$1E$2"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?)", "S$1"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s+[CС]езоны?", "S$1"]
        - name: re_replace
          args: ["(?i)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)", "E$1 of $2"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "E$1 of $2"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s+(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))\\s+из\\s*(\\w?)", "E$1 of $2"]
        - name: re_replace
          args: ["(?i)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)", "E$1"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s+(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "E$1"]
        - name: re_replace
          args: ["(\\([\\p{IsCyrillic}\\W]+\\))|(^[\\p{IsCyrillic}\\W\\d]+\\/ )|([\\p{IsCyrillic} \\-]+,+)|([\\p{IsCyrillic}]+)", "{{ if .Config.stripcyrillic }}{{ else }}$1$2$3$4{{ end }}"]
        - name: re_replace
          args: ["(?i)\\bHDTV[-\\s]?Rip\\b", "HDTV"]
        - name: re_replace
          args: ["(?i)\\bSAT[-\\s]?Rip\\b", "HDTV"]
        - name: re_replace
          args: ["(?i)\\bWEB[-\\s]?DL[-\\s]?Rip\\b", "WEB-DL"]
        - name: re_replace
          args: ["(?i)\\bWEB\\sRip\\b", "WEBRip"]
        - name: re_replace
          args: ["(?i)\\bWEB\\sDL\\b", "WEB-DL"]
        - name: replace
          args: ["BD720p", "BD 720p"]
        - name: replace
          args: ["HD720p", "HD 720p"]
        - name: replace
          args: ["HDR720p", "HDR 720p"]
        - name: replace
          args: ["BD1080p", "BD 1080p"]
        - name: replace
          args: ["HD1080p", "HD 1080p"]
        - name: replace
          args: ["HDR1080p", "HDR 1080p"]
        - name: replace
          args: [" (Золото)", ""] # remove gold tag
        - name: re_replace
          args: ["[\\[\\(\\{<«][\\s\\W]*[\\]\\)\\}>»]", ""]
        - name: re_replace
          args: ["^[\\s&,\\.!\\?\\+\\-_\\|\\/':]+", ""]
        - name: re_replace
          args: ["^\\((.+?)\\s?\\)", "$1 "]
        - name: append
          args: "{{ if .Config.addrussiantotitle }} RUS{{ else }}{{ end }}"
    details:
      selector: a[href^="details.php?id="]
      attribute: href
    download:
      selector: a[href^="download.php?id="]
      attribute: href
    date:
      # 2022-03-28 01:28:07
      selector: li[title="Дата"]
      filters:
        - name: append
          args: " +03:00" # MSK
        - name: dateparse
          args: "yyyy-MM-dd HH:mm:ss zzz"
    files:
      selector: li[title="Файлов"]
    size:
      selector: li[title="Размер"]
    seeders:
      selector: li[title="Активность"]
      filters:
        - name: regexp
          args: (\d+)\s+\d+
    leechers:
      selector: li[title="Активность"]
      filters:
        - name: regexp
          args: \d+\s+(\d+)
    grabs:
      selector: li[title="Завершено"]
    downloadvolumefactor:
      case:
        "a[href^=\"details.php?id=\"]:contains(\"(Золото)\")": 0
        "*": 1
    uploadvolumefactor:
      text: 1
    minimumratio:
      text: 0.3
    description:
      selector: a[href^="details.php?id="]
# engine n/a
