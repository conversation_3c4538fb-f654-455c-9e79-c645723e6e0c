---
id: ptfans
name: PTFans
description: "PTFans is a CHINESE Private Torrent Tracker for MOVIES / TV / GENERAL"
language: zh-CN
type: private
encoding: UTF-8
requestDelay: 2
links:
  - https://ptfans.cc/
legacylinks:
  - https://cusat.win/

caps:
  # dont forget to update the path categories in the search block
  categorymappings:
    - {id: 401, cat: Movies, desc: "Movies/电影", default: true}
    - {id: 404, cat: TV, desc: "TV Series/电视剧", default: true}
    - {id: 405, cat: TV, desc: "TV Shows/综艺", default: true}
    - {id: 406, cat: TV/Documentary, desc: "Documentaries/纪录片", default: true}
    - {id: 403, cat: TV/Sport, desc: "Sport/体育、竞技、武术及相关", default: true}
    - {id: 409, cat: PC/Games, desc: "Games/游戏及相关", default: true}
    - {id: 407, cat: Audio, desc: "Music/音乐、专辑、MV、演唱会", default: true}
    - {id: 408, cat: Other, desc: "Art/曲艺、相声、小品、戏曲、舞蹈、歌剧、评书等", default: true}
    - {id: 410, cat: Other, desc: "Science/科学、知识、技能", default: true}
    - {id: 411, cat: Other, desc: "School/应试、考级、职称、初中以上教育", default: true}
    - {id: 412, cat: Books/EBook, desc: "Book/书籍、杂志、报刊、有声书", default: true}
    - {id: 413, cat: Other, desc: "Code/IT技术、建模、编程、信息技术、大数据、人工智能", default: true}
    - {id: 414, cat: TV/Anime, desc: "Animate/3D动画、2.5次元", default: true}
    - {id: 415, cat: Other, desc: "ACGN/二次元、漫画", default: true}
    - {id: 416, cat: Other, desc: "Baby/婴幼、儿童、早教、小学及相关", default: true}
    - {id: 417, cat: Other, desc: "Resource/素材、数据、图片、文档、模板", default: true}
    - {id: 418, cat: PC/0day, desc: "Software/软件、系统、 程序、APP等", default: true}
    - {id: 419, cat: Other, desc: "Other/其它，确认上边分类无", default: true}
    - {id: 420, cat: XXX, desc: "步兵/步兵/无码", default: false}
    - {id: 421, cat: XXX, desc: "骑兵/骑兵/有码", default: false}
    - {id: 422, cat: XXX, desc: "三级/三级片、限制级电影", default: false}
    - {id: 423, cat: XXX, desc: "H漫/动漫、漫画", default: false}
    - {id: 424, cat: XXX, desc: "H游/游戏及相关", default: false}
    - {id: 425, cat: XXX, desc: "H书/书籍、有声书", default: false}
    - {id: 426, cat: XXX, desc: "H图/写真、图片、私拍、短视频", default: false}
    - {id: 427, cat: XXX, desc: "H音/ASMR、音频、音乐", default: false}
    - {id: 428, cat: XXX, desc: "H综/综艺、综合、剪辑、其他等", default: false}
    - {id: 429, cat: XXX, desc: "H同/男同、女同、人妖", default: false}

  modes:
    search: [q]
    tv-search: [q, season, ep, imdbid]
    movie-search: [q, imdbid]
    music-search: [q]
    book-search: [q]

settings:
  - name: username
    type: text
    label: Username
  - name: password
    type: password
    label: Password
  - name: 2facode
    type: text
    label: 2FA code
  - name: info_2fa
    type: info
    label: "About 2FA code"
    default: "Only fill in the <b>2FA code</b> box if you have enabled <b>2FA</b> on the HDFans Web Site. Otherwise just leave it empty."
  - name: freeleech
    type: checkbox
    label: Search freeleech only
    default: false
  - name: sort
    type: select
    label: Sort requested from site
    default: 4
    options:
      4: created
      7: seeders
      5: size
      1: title
  - name: type
    type: select
    label: Order requested from site
    default: desc
    options:
      desc: desc
      asc: asc
  - name: info_tpp
    type: info
    label: Results Per Page
    default: For best results, change the <b>Torrents per page:</b> setting to <b>100</b> on your account profile.
  - name: info_activity
    type: info
    label: Account Inactivity
    default: "Account retention rules:<ol><li>Veteran User and above will be retained forever</li><li>Elite User and above will not have their account deleted after parking (in the control panel)</li><li> Users with a parked account will be deleted if they do not log in for 400 consecutive days</li><li>Users with a non-parked account will be deleted if they do not log in for 150 consecutive days</li><li>Users who have no traffic (i.e. upload/download data are both 0) will be deleted if they do not log in for 100 consecutive days.</li></ol>"

login:
  path: login.php
  method: form
  form: form[action="takelogin.php"]
  captcha:
    type: image
    selector: img[alt="CAPTCHA"]
    input: imagestring
  inputs:
    secret: ""
    username: "{{ .Config.username }}"
    password: "{{ .Config.password }}"
    two_step_code: "{{ .Config.2facode }}"
    logout: ""
    securelogin: ""
    ssl: yes
    trackerssl: yes
  error:
    - selector: td.embedded:has(h2:contains("失败"))
      message:
        selector: td.text
  test:
    path: index.php
    selector: a[href="logout.php"]

search:
  paths:
    - path: torrents.php
      categories: [401, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419]
    - path: special.php
      categories: [420, 421, 422, 423, 424, 425, 426, 427, 428, 429]
  inputs:
    $raw: "{{ range .Categories }}cat{{.}}=1&{{end}}"
    search: "{{ if .Query.IMDBID }}{{ .Query.IMDBID }}{{ else }}{{ end }}{{ if or .Query.IMDBID .Query.DoubanID }} {{ else }}{{ .Keywords }}{{ end }}{{ if .Query.DoubanID }}{{ .Query.DoubanID }}{{ else }}{{ end }}"
    # 0 incldead, 1 active, 2 dead
    incldead: 0
    # 0 all, 1 normal, 2 free, 3 2x, 4 2xfree, 5 50%, 6 2x50%, 7 30%
    spstate: "{{ if .Config.freeleech }}2{{ else }}0{{ end }}"
    # 0 title, 1 descr, 3 uploader, 4 imdburl
    search_area: "{{ if .Query.IMDBID }}4{{ else }}{{ end }}{{ if .Query.DoubanID }}1{{ else }}{{ end }}{{ if or .Query.IMDBID .Query.DoubanID }}{{ else }}0{{ end }}"
    # 0 AND, 2 exact
    search_mode: 0
    sort: "{{ .Config.sort }}"
    type: "{{ .Config.type }}"
    notnewword: 1

  rows:
    selector: table.torrents > tbody > tr:has(a[href^="download.php?id="])

  fields:
    category:
      selector: a[href^="?cat="]
      attribute: href
      filters:
        - name: querystring
          args: cat
    title_default:
      selector: a[href^="details.php?id="]
    title:
      selector: a[title][href^="details.php?id="]
      attribute: title
      optional: true
      default: "{{ .Result.title_default }}"
    details:
      selector: a[href^="details.php?id="]
      attribute: href
    download:
      selector: a[href^="download.php?id="]
      attribute: href
    imdbid:
      # site currently only has a badge and rating, the id is not present. just in case a future update.
      selector: a[href*="imdb.com/title/tt"]
      attribute: href
    doubanid:
      # site currently only has a badge and rating, the id is not present. just in case a future update.
      selector: a[href*="movie.douban.com/subject/"]
      attribute: href
    date_elapsed:
      # time type: time elapsed (default)
      selector: td.rowfollow:nth-child(4) > span[title]
      attribute: title
      optional: true
      filters:
        - name: append
          args: " +00:00" # GMT
        - name: dateparse
          args: "yyyy-MM-dd HH:mm:ss zzz"
    date_added:
      # time added
      selector: td.rowfollow:nth-child(4):not(:has(span))
      optional: true
      filters:
        - name: append
          args: " +00:00" # GMT
        - name: dateparse
          args: "yyyy-MM-ddHH:mm:ss zzz"
    date:
      text: "{{ if or .Result.date_elapsed .Result.date_added }}{{ or .Result.date_elapsed .Result.date_added }}{{ else }}now{{ end }}"
    size:
      selector: td.rowfollow:nth-child(5)
      optional: true
      default: 512MB
    seeders:
      selector: td.rowfollow:nth-child(6)
      optional: true
      default: 0
    leechers:
      selector: td.rowfollow:nth-child(7)
      optional: true
      default: 0
    grabs:
      selector: td.rowfollow:nth-child(8)
      optional: true
      default: 0
    downloadvolumefactor:
      case:
        img.pro_free: 0
        img.pro_free2up: 0
        img.pro_50pctdown: 0.5
        img.pro_50pctdown2up: 0.5
        img.pro_30pctdown: 0.3
        "*": 1
    uploadvolumefactor:
      case:
        img.pro_50pctdown2up: 2
        img.pro_free2up: 2
        img.pro_2up: 2
        "*": 1
    minimumseedtime:
      # 2 days (as seconds = 2 x 24 x 60 x 60)
      text: 172800
    description:
      selector: td.rowfollow:nth-child(2)
      remove: a, b, font, img, span
# NexusPHP v1.8.5 2023-07-29
