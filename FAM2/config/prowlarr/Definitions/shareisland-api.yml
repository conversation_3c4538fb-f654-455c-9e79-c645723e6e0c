---
id: shareisland-api
name: Shareisland (API)
description: "Shareisland is a ITALIAN Private Torrent Tracker for MOVIES / TV / GENERAL"
language: it-IT
type: private
encoding: UTF-8
links:
  - https://shareisland.org/
legacylinks:
  - http://shareisland.org/
  - http://www.shareisland.org/

caps:
  categorymappings:
    - {id: 1, cat: Movies, desc: "Movie"}
    - {id: 2, cat: TV, desc: "Serie TV"}
    - {id: 15, cat: Books/EBook, desc: "Ebook"}
    - {id: 17, cat: Books/Mags, desc: "Riviste e Giornali"}
    - {id: 19, cat: XXX, desc: "XXX"}
    - {id: 3, cat: Audio, desc: "Music"}
    - {id: 7, cat: PC/Games, desc: "Games"}
    - {id: 23, cat: PC, desc: "Software"}
    - {id: 24, cat: TV/Sport, desc: "EVENTI SPORTIVI"}
    - {id: 25, cat: Other, desc: "Misc"}

  modes:
    search: [q]
    tv-search: [q, season, ep, imdbid, tvdbid, tmdbid]
    movie-search: [q, imdbid, tmdbid]
    music-search: [q]
    book-search: [q]

settings:
  - name: apikey
    type: text
    label: APIKey
  - name: info_key
    type: info
    label: About your API key
    default: "Find or Generate a new API Token by accessing your <a href=\"https://shareisland.org/\" target=\"_blank\">Shareisland</a> account <i>My Settings</i> page and clicking on the <b>API Key</b> tab."
  - name: freeleech
    type: checkbox
    label: Search freeleech only
    default: false
  - name: single_file_release_use_filename
    type: checkbox
    label: Use filename as title for single file releases
    default: true
  - name: sort
    type: select
    label: Sort requested from site
    default: created_at
    options:
      created_at: created
      seeders: seeders
      size: size
      name: title
  - name: type
    type: select
    label: Order requested from site
    default: desc
    options:
      desc: desc
      asc: asc
  - name: info_activity
    type: info
    label: Account Inactivity
    default: "After 60 days of no activity, your account will be automatically deleted."

login:
  path: /api/torrents
  method: get
  inputs: {} # TODO: remove in v10
  error:
    - selector: a[href*="/login"]
      message:
        text: "The API key was not accepted by {{ .Config.sitelink }}."
    - selector: :root:contains("Account is Banned")

search:
  paths:
    # https://hdinnovations.github.io/UNIT3D/torrent_api.html
    # https://github.com/HDInnovations/UNIT3D/blob/master/app/Http/Controllers/API/TorrentController.php#L657
    - path: api/torrents/filter
      response:
        type: json

  headers:
    Authorization: ["Bearer {{ .Config.apikey }}"]

  inputs:
  # if we have an id based search, add Season and Episode as query in name for UNIT3D < v6.  Else pass S/E Params for UNIT3D >= v6
    $raw: "{{ range .Categories }}&categories[]={{.}}{{end}}"
    name: "{{ .Keywords }}"
    seasonNumber: "{{ .Query.Season }}"
    episodeNumber: "{{ .Query.Ep }}"
    imdbId: "{{ .Query.IMDBIDShort }}"
    tmdbId: "{{ .Query.TMDBID }}"
    tvdbId: "{{ .Query.TVDBID }}"
    "free[]": "{{ if .Config.freeleech }}100{{ else }}{{ end }}"
    sortField: "{{ .Config.sort }}"
    sortDirection: "{{ .Config.type }}"
    perPage: 100

  keywordsfilters:
    # strip season and/or ep
    - name: re_replace
      args: ["\\b([SE]\\d{1,4}){1,2}\\b", ""]
    - name: diacritics
      args: replace
    - name: re_replace
      args: ["\\.", " "]

  rows:
    selector: data
    attribute: attributes

  fields:
    category:
      selector: category_id
    title_optional:
      selector: name
    title_filename:
      selector: "files[0].name"
      optional: true
    files:
      selector: num_file
    title:
      text: "{{ if and (.Config.single_file_release_use_filename) (eq .Result.files \"1\") (.Result.title_filename) }}{{ .Result.title_filename }}{{ else }}{{ .Result.title_optional }}{{ end }}"
      filters:
        - name: re_replace # replace special characters with " " (space)
          args: ["[\\[!\"#$%&'()*+,\\-.\\/:;<=>?@[\\]^_`{|}~]", " "]
        - name: diacritics
          args: replace
        - name: re_replace # replace multiple spaces
          args: ["[ ]{2,}", " "]
        # normalize to SXXEYY format
        - name: re_replace # S01 E01 to S01E01
          args: ["(?i)\\bS(\\d+)\\sE(\\d+)\\b", "S$1E$2"]
        - name: re_replace # 01x01 to S01E01
          args: ["(?i)(\\d{2})x(\\d+)", "S$1E$2"]
        - name: re_replace # 1x01 to S01E01
          args: ["(?i)\\b(\\d{1})x(\\d+)", "S0$1E$2"]
        - name: re_replace # Stagione X --> S0X
          args: ["(?i)\\bStagion[ei]\\s?(\\d{1})\\b|\\bSeason'?s?\\s?(\\d{1})\\b", "S0$1$2"]
        - name: re_replace # Stagione XX --> SXX
          args: ["(?i)\\bStagion[ei]\\s?(\\d{2,})\\b|\\bSeason'?s?\\s?(\\d{2,})\\b", "S$1$2"]
        - name: re_replace # Episodio 4 to E4
          args: ["(?i)\\b(?:[\\/\\|]?Episodio\\s?(\\d+)|Puntata\\s?(\\d+))", "E$1$2"]
        - name: re_replace # Episodi 4 5 to E04-05
          args: ["(?i)\\b(?:Puntate\\s*)(\\d+)\\s?(\\d+)", "E0$1-0$2"]
        - name: re_replace # rimozioni varie
          args: ["(?i)(Serie completa|Completat?a?|in pausa)", ""]
    details:
      selector: details_link
    download:
      selector: download_link
    infohash:
      selector: info_hash
    poster:
      selector: meta.poster
      filters:
        - name: replace
          args: ["https://via.placeholder.com/90x135", ""]
    imdbid:
      selector: imdb_id
    tmdbid:
      selector: tmdb_id
    tvdbid:
      selector: tvdb_id
    genre:
      selector: meta.genres
      filters:
        - name: re_replace
          args: ["(?i)(televisione film)", "televisione_film"]
        - name: replace
          args: [" & ", "_&_"]
    description:
      text: "{{ .Result.genre }}"
    seeders:
      selector: seeders
    leechers:
      selector: leechers
    grabs:
      selector: times_completed
    date:
      # "created_at": "2021-10-18T00:34:50.000000Z" is returned by Newtonsoft.Json.Linq as 18/10/2021 00:34:50
      selector: created_at
      filters:
        - name: append
          args: " +01:00" # CET
        - name: dateparse
          args: "MM/dd/yyyy HH:mm:ss zzz"
    size:
      selector: size
    _featured:
      selector: featured
      case:
        False: "{{ .False }}"
        True: "{{ .True }}"
    downloadvolumefactor_freeleech:
      # api returns 0%, 25%, 50%, 75%, 100%
      selector: freeleech
      case:
        0%: 1 # not free
        25%: 0.75
        50%: 0.5
        75%: 0.25
        100%: 0 # freeleech
        "*": 0 # catch errors
    downloadvolumefactor:
      text: "{{ if .Result._featured }}0{{ else }}{{ .Result.downloadvolumefactor_freeleech }}{{ end }}"
    uploadvolumefactor_double_upload:
      # api returns False, True
      selector: double_upload
      case:
        False: 1 # normal
        True: 2 # double
    uploadvolumefactor:
      text: "{{ if .Result._featured }}2{{ else }}{{ .Result.uploadvolumefactor_double_upload }}{{ end }}"
# global MR is 0.4 but torrents must be seeded for 6 days regardless of ratio
#    minimumratio:
#      text: 0.4
    minimumseedtime:
      # 6 days (as seconds = 6 x 24 x 60 x 60)
      text: 518400
# json UNIT3D 9.1.2 (custom)
