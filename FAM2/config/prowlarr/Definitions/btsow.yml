---
id: btsow
name: BTSOW
description: "BTSOW is a Public torrent indexer"
language: en-US
type: public
encoding: UTF-8
# use https://tellme.pw/btsow to find the new domain
links:
  - https://btsow.lol/
legacylinks:
  - https://btsow.cfd/
  - https://btsow.sbs/
  - https://btsow.quest/
  - https://btsow.autos/
  - https://btsow.beauty/
  - https://btsow.mom/
  - https://btsow.boats/
  - https://btsow.hair/
  - https://btsow.bond/
  - https://btsow.skin/
  - https://btsow.makeup/
  - https://btsow.homes/
  - https://btsow.yachts/
  - https://btsow.motorcycles/
  - https://btsow.pics/

caps:
  categories:
    Other: Other

  modes:
    search: [q]
    tv-search: [q, season, ep]
    movie-search: [q]
    music-search: [q]
    book-search: [q]

settings:
  - name: info_category_8000
    type: info_category_8000

download:
  selectors:
    - selector: a#magnetOpen
      attribute: href

search:
  paths:
    - path: "search/{{ if .Keywords }}{{ .Keywords }}{{ else }}{{ .Today.Year }}{{ end }}"

  headers:
    # site blocks Jackett's User-Agents, so slightly alter it here (e.g. Safari/537.36 > Safari/537.35)
    User-Agent: ["Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.35"]

  rows:
    selector: div.row:has(a[href*="/detail/hash/"])

  fields:
    category:
      text: Other
    title:
      selector: a[href*="/detail/hash/"]
      attribute: title
    details:
      selector: a[href*="/detail/hash/"]
      attribute: href
    download:
      selector: a[href*="/detail/hash/"]
      attribute: href
    date:
      selector: div.date
      filters:
        - name: append
          args: " -00:00" # GMT
        - name: dateparse
          args: "yyyy-MM-dd zzz"
    size:
      selector: div.size
    seeders:
      text: 1
    leechers:
      text: 1
    downloadvolumefactor:
      text: 0
    uploadvolumefactor:
      text: 1
# engine n/a
