---
id: tapochek
name: <PERSON><PERSON><PERSON><PERSON>
description: "<PERSON><PERSON><PERSON><PERSON> is a RUSSIAN Private Torrent Tracker for 0DAY / GENERAL"
language: ru-RU
type: private
encoding: windows-1251
links:
  - https://tapochek.net/
legacylinks:
  - http://tapochek.net/

caps:
  categorymappings:
    # PC Games      # Игры для PC
    - {id: 2, cat: PC/Games, desc: "Игры для PC (в разработке, разное, обсуждения)"}
    - {id: 14, cat: PC/Games, desc: "Игры в разработке и демо-версии"}
    - {id: 9, cat: PC/Games, desc: "Разное (дополнения, патчи, русификаторы)"}
    - {id: 18, cat: PC/Games, desc: "Горячие новинки"}
    - {id: 24, cat: PC/Games, desc: "Приключения и квесты"}
    - {id: 25, cat: PC/Games, desc: "Стратегии"}
    - {id: 19, cat: PC/Games, desc: "Аркады"}
    - {id: 29, cat: PC/Games, desc: "Симуляторы"}
    - {id: 35, cat: PC/Games, desc: "Экшены"}
    - {id: 40, cat: PC/Games, desc: "Ролевые игры"}
    - {id: 41, cat: PC/Games, desc: "Эротические игры"}
    - {id: 43, cat: PC/Games, desc: "Эротические"}
    - {id: 42, cat: PC/Games, desc: "Хентайные"}
    - {id: 968, cat: PC/Games, desc: "Игры для VR"}
    - {id: 46, cat: PC/Games, desc: "Для самых маленьких"}
    - {id: 978, cat: PC/Games, desc: "Игры для macOS и Linux"}
    # Games for Consoles      # Игры для Консолей
    - {id: 69, cat: Console/XBox, desc: "Xbox"}
    - {id: 86, cat: Console/XBox 360, desc: "XBox360 | Игры"}
    - {id: 646, cat: Console/XBox 360, desc: "XBox360 | JTAG"}
    - {id: 87, cat: Console/XBox 360, desc: "XBox360 | 360E"}
    - {id: 89, cat: Console/XBox 360, desc: "XBox360 | Demo"}
    - {id: 612, cat: Console/XBox 360, desc: "XBox360 | Live"}
    - {id: 614, cat: Console/XBox 360, desc: "XBox360 | Soft"}
    - {id: 88, cat: Console/XBox, desc: "XBox | Игры"}
    - {id: 70, cat: Console/PSP, desc: "PlayStation"}
    - {id: 973, cat: Console/PS4, desc: "PS5 | Игры"}
    - {id: 910, cat: Console/PS4, desc: "PS4 | Игры"}
    - {id: 911, cat: Console/PS4, desc: "PS4 | Сцена"}
    - {id: 689, cat: Console/PS3, desc: "PS3 | Игры"}
    - {id: 818, cat: Console/PS3, desc: "PS3 | Сцена"}
    - {id: 696, cat: Console/PSP, desc: "PSN | Игры"}
    - {id: 853, cat: Console/PS3, desc: "PS3 | Emulation"}
    - {id: 904, cat: Console/PS Vita, desc: "PS Vita | Игры"}
    - {id: 102, cat: Console/PSP, desc: "PSP | Игры"}
    - {id: 103, cat: Console/PS3, desc: "PS2 | Игры"}
    - {id: 104, cat: Console/PSP, desc: "PSX | Игры"}
    - {id: 435, cat: Console/PSP, desc: "PSP | Сцена"}
    - {id: 105, cat: Console/PSP, desc: "PSP | PSX-PSP"}
    - {id: 690, cat: Console/PS3, desc: "PS3 | Прочее"}
    - {id: 178, cat: Console/PSP, desc: "PSP | Прочее"}
    - {id: 78, cat: Console/NDS, desc: "Nintendo"}
    - {id: 148, cat: Console/NDS, desc: "Архив (Nintendo)"}
    - {id: 116, cat: Console/NDS, desc: "NDS | Игры"}
    - {id: 885, cat: Console/3DS, desc: "3DS | Игры"}
    - {id: 912, cat: Console, desc: "Switch | Игры"}
    - {id: 115, cat: Console/Wii, desc: "Wii | Игры"}
    - {id: 899, cat: Console/WiiU, desc: "Wii U | Игры"}
    - {id: 900, cat: Console/WiiU, desc: "Wii U | Сцена"}
    - {id: 381, cat: Console/Wiiware, desc: "WiiWare | Игры"}
    - {id: 382, cat: Console, desc: "GameCube | Игры"}
    - {id: 349, cat: Console, desc: "Sega"}
    - {id: 350, cat: Console, desc: "Dreamcast | Игры"}
    - {id: 351, cat: Console, desc: "Saturn | Игры"}
    - {id: 352, cat: Console, desc: "Игры для старых консолей"}
    - {id: 390, cat: Console, desc: "Коллекции (игры для разных платформ)"}
    # Cinema      # Кинематограф
    - {id: 964, cat: Movies/UHD, desc: "Новинки | UHD 4K"}
    - {id: 431, cat: Movies/WEB-DL, desc: "Новинки | HD"}
    - {id: 430, cat: Movies, desc: "Новинки | Rips"}
    - {id: 703, cat: Movies, desc: "Новинки | CAMRip, TS, WEBRip(LQ)"}
    - {id: 963, cat: Movies/UHD, desc: "Зарубежное кино | UHD 4K"}
    - {id: 122, cat: Movies/HD, desc: "Зарубежное кино | HD"}
    - {id: 124, cat: Movies/Foreign, desc: "Зарубежное кино | Rips"}
    - {id: 123, cat: Movies, desc: "Сборники фильмов"}
    - {id: 974, cat: Movies/UHD, desc: "Отечественное кино | UHD 4K"}
    - {id: 128, cat: Movies/HD, desc: "Отечественное кино | HD Rips"}
    - {id: 133, cat: Movies/DVD, desc: "Отечественное кино | DVD"}
    - {id: 131, cat: Movies, desc: "Российское и советское кино | Rips"}
    - {id: 869, cat: Movies/3D, desc: "3D"}
    - {id: 886, cat: Movies, desc: "Новинки зарубежных сериалов (2024-2025)"}
    - {id: 160, cat: TV, desc: "Отечественные cериалы"}
    - {id: 135, cat: Movies, desc: "Зарубежные сериалы до 2025г."}
    - {id: 979, cat: TV, desc: "Русские сериалы до 2025г"}
    - {id: 325, cat: TV/UHD, desc: "Мультфильмы | UHD 4K"}
    - {id: 324, cat: TV/HD, desc: "Мультфильмы | HD Rips"}
    - {id: 328, cat: TV, desc: "Мультфильмы | Rips"}
    - {id: 330, cat: TV, desc: "Сборники мультфильмов"}
    - {id: 321, cat: TV, desc: "Мультсериалы"}
    - {id: 162, cat: TV/Documentary, desc: "Документальные фильмы и телешоу"}
    # Anime      # Аниме
    - {id: 693, cat: TV/Anime, desc: "Аниме (Основной)"}
    - {id: 660, cat: TV/Anime, desc: "Онгоинги и новинки аниме"}
    - {id: 95, cat: TV/Anime, desc: "Аниме (HD)"}
    - {id: 684, cat: TV/Anime, desc: "Аниме (Rips | DVD)"}
    - {id: 106, cat: TV/Anime, desc: "Манга и прочий арт"}
    - {id: 680, cat: TV/Anime, desc: "Аниме (Хентай)"}
    - {id: 682, cat: TV/Anime, desc: "UnCensored"}
    - {id: 681, cat: TV/Anime, desc: "Censored"}
    - {id: 697, cat: TV/Anime, desc: "Манга, обои, артбуки и др."}
    # Music and Music Video      # Музыка и Музыкальное видео
    - {id: 450, cat: Audio, desc: "Общий раздел музыки"}
    - {id: 470, cat: Audio, desc: "Классическая музыка"}
    - {id: 495, cat: Audio, desc: "New Age, Relax, Meditative & Flamenco"}
    - {id: 456, cat: Audio, desc: "Jazz, Blues"}
    - {id: 462, cat: Audio, desc: "Reggae, Ska, Dub"}
    - {id: 491, cat: Audio, desc: "Фольклор, Народная и Этническая музыка"}
    - {id: 468, cat: Audio, desc: "Зарубежный Rock"}
    - {id: 469, cat: Audio/Lossless, desc: "Rосk, Mеtаl, Аltеrnаtivе, Рunk, Indереndеnt (lоsslеss)"}
    - {id: 472, cat: Audio/MP3, desc: "Rосk, Mеtаl, Аltеrnаtivе, Рunk, Indереndеnt (mp3)"}
    - {id: 476, cat: Audio, desc: "Отечественный Rock"}
    - {id: 477, cat: Audio/Lossless, desc: "Rосk, Metal, Punk, Alternative (losslеss)"}
    - {id: 479, cat: Audio/MP3, desc: "Rосk, Metal, Punk, Alternative (mр3)"}
    - {id: 482, cat: Audio, desc: "Поп-музыка, Eurodance, Disco"}
    - {id: 483, cat: Audio, desc: "Зарубежная Поп-музыка"}
    - {id: 484, cat: Audio, desc: "Отечественная Поп-музыка"}
    - {id: 485, cat: Audio, desc: "Eurodance, Technopop, Disco"}
    - {id: 489, cat: Audio/MP3, desc: "Поп-музыка, Eurodance, Disco (сборники) (mp3)"}
    - {id: 503, cat: Audio, desc: "Rap, Hip-Hop, R'n'B"}
    - {id: 504, cat: Audio, desc: "Зарубежный Rap, Hip-Hop, R'n'B"}
    - {id: 505, cat: Audio, desc: "Отечественный Rap, Hip-Hop, R'n'B"}
    - {id: 509, cat: Audio, desc: "Электронная музыка"}
    - {id: 510, cat: Audio, desc: "Trance, Goa Trance, Psy-Trance, PsyChill, Ambient Dub"}
    - {id: 511, cat: Audio, desc: "House, Techno, Hardcore, Hardstyle, Jumpstyle"}
    - {id: 512, cat: Audio, desc: "Drum & Bass, Jungle, Breakbeat, Dubstep, IDM"}
    - {id: 513, cat: Audio, desc: "Chillout, Lounge, Downtempo, Trip-Hop"}
    - {id: 514, cat: Audio, desc: "Traditional Electronic, Ambient, Experimental"}
    - {id: 515, cat: Audio, desc: "Industrial, Noise, EBM, Dark Electro, Aggrotech, Synthpop, N.."}
    - {id: 534, cat: Audio, desc: "Музыка разное"}
    - {id: 396, cat: Audio, desc: "Саундтреки"}
    - {id: 542, cat: Audio, desc: "Неофициальные и внежанровые сборники"}
    - {id: 547, cat: Audio, desc: "Hi-Res stereo"}
    - {id: 546, cat: Audio, desc: "Собственные оцифровки"}
    - {id: 535, cat: Audio, desc: "Музыка других жанров"}
    - {id: 539, cat: Audio, desc: "Музыкальное видео"}
    # Software      # Программное обеспечение
    - {id: 639, cat: TV, desc: "Обучающие видеоматериалы"}
    - {id: 202, cat: TV, desc: "macOS и ПО под них"}
    - {id: 274, cat: TV, desc: "Веб-разработка и программирование"}
    - {id: 303, cat: TV, desc: "Материалы для мультимедиа и дизайна"}
    - {id: 969, cat: PC, desc: "Windows 11"}
    - {id: 905, cat: PC, desc: "Windows 10"}
    - {id: 230, cat: PC, desc: "Windows 7"}
    - {id: 227, cat: PC, desc: "Настольные OS, выпущенные до 2001 года (Microsoft Windows OS.."}
    - {id: 970, cat: PC, desc: "Сборки (Microsoft Windows 11)"}
    - {id: 906, cat: PC, desc: "Сборки (Microsoft Windows 10)"}
    - {id: 701, cat: PC, desc: "Сборки (Microsoft Windows 7)"}
    - {id: 231, cat: PC, desc: "Серверные (Microsoft Windows OS)"}
    - {id: 232, cat: PC, desc: "Разное (Microsoft Windows, WinXP, Vista, 8)"}
    - {id: 236, cat: PC, desc: "Работа с жёстким диском"}
    - {id: 237, cat: PC, desc: "Резервное копирование"}
    - {id: 240, cat: PC, desc: "Архиваторы и файловые менеджеры"}
    - {id: 239, cat: PC, desc: "Программы для настройки и оптимизации ОС"}
    - {id: 241, cat: PC, desc: "Сервисное обслуживание компьютера"}
    - {id: 242, cat: PC, desc: "Работа с носителями информации"}
    - {id: 243, cat: PC, desc: "Информация и диагностика"}
    - {id: 244, cat: PC, desc: "Программы для интернет и сетей"}
    - {id: 245, cat: PC, desc: "Антивирусы и Файерволы"}
    - {id: 246, cat: PC, desc: "Программы для защиты информации"}
    - {id: 247, cat: PC, desc: "Драйвера"}
    - {id: 248, cat: PC, desc: "Серверное ПО для Windows"}
    - {id: 249, cat: PC, desc: "Изменение интерфейса ОС Windows"}
    - {id: 250, cat: PC, desc: "Скринсейверы"}
    - {id: 251, cat: PC, desc: "Разное (Системные программы под Windows)"}
    - {id: 409, cat: PC, desc: "Системы автоматизации проектных работ (САПР)"}
    - {id: 607, cat: PC, desc: "Программы для архитекторов и дизайнеров интерьеров"}
    - {id: 593, cat: PC, desc: "Офисные программы и системы"}
    - {id: 595, cat: PC, desc: "Системы для научной работы"}
    - {id: 594, cat: PC, desc: "Системы для бизнеса"}
    - {id: 257, cat: PC, desc: "Каталогизаторы и просмотрщики графики"}
    - {id: 258, cat: PC, desc: "Аудио- и видео-, CD- проигрыватели и каталогизаторы"}
    - {id: 259, cat: PC, desc: "Программы для интернет и сетей"}
    - {id: 260, cat: PC, desc: "Распознавание текста, звука и синтез речи"}
    - {id: 261, cat: PC, desc: "Словари и переводчики"}
    - {id: 262, cat: PC, desc: "Программное обеспечение для автолюбителей"}
    - {id: 263, cat: PC, desc: "Медицинское программное обеспечение"}
    - {id: 264, cat: PC, desc: "Справочные системы и карты"}
    - {id: 265, cat: PC, desc: "Разное (Пользовательские программы)"}
    - {id: 278, cat: PC, desc: "Программные комплекты"}
    - {id: 280, cat: PC, desc: "Графические редакторы"}
    - {id: 281, cat: PC, desc: "Программы для верстки, печати и работы со шрифтами"}
    - {id: 282, cat: PC, desc: "3D моделирование, рендеринг и плагины для них"}
    - {id: 283, cat: PC, desc: "Анимация"}
    - {id: 285, cat: PC, desc: "Редакторы видео"}
    - {id: 286, cat: PC, desc: "Видео- Аудио- конверторы"}
    - {id: 287, cat: PC, desc: "Работа со звуком"}
    - {id: 290, cat: PC, desc: "Разное (Программы для работы с мультимедиа и 3D)"}
    - {id: 623, cat: PC, desc: "Мобильные телефоны"}
    - {id: 624, cat: PC, desc: "КПК"}
    - {id: 625, cat: PC, desc: "Навигаторы"}
    # Library      # Библиотека
    - {id: 717, cat: Books, desc: "Аудиокниги"}
    - {id: 738, cat: Books, desc: "Детектив / боевик"}
    - {id: 739, cat: Books, desc: "Фантастика / фэнтези / мистика / ужасы"}
    - {id: 836, cat: Books, desc: "Роман / комедийные произведения / приключения"}
    - {id: 838, cat: Books, desc: "Классическая литература и современная проза"}
    - {id: 837, cat: Books, desc: "Образование и Право"}
    - {id: 843, cat: Books, desc: "Разное"}
    - {id: 713, cat: Books, desc: "Художественная литература"}
    - {id: 724, cat: Books, desc: "Детектив / боевик"}
    - {id: 728, cat: Books, desc: "Фантастика / фэнтези / мистика / ужасы"}
    - {id: 725, cat: Books, desc: "Роман / приключения / драма"}
    - {id: 727, cat: Books, desc: "Комедийные произведения"}
    - {id: 723, cat: Books, desc: "Историческая книга"}
    - {id: 721, cat: Books, desc: "Классическая литература и современная проза"}
    - {id: 722, cat: Books, desc: "Поэзия"}
    - {id: 844, cat: Books, desc: "Разное"}
    - {id: 718, cat: Books, desc: "Журналы"}
    - {id: 763, cat: Books, desc: "Эротические журналы"}
    - {id: 715, cat: Books, desc: "Компьютерная литература"}
    - {id: 839, cat: Books, desc: "Научная и тех.литература"}
    - {id: 719, cat: Books, desc: "Образование, Право и Психология"}
    - {id: 846, cat: Books, desc: "Сексология"}
    - {id: 840, cat: Books, desc: "Военное дело"}
    - {id: 842, cat: Books, desc: "Спорт и физическое воспитание"}
    - {id: 841, cat: Books, desc: "Хобби"}
    - {id: 716, cat: Books, desc: "Дом и семейный очаг"}
    - {id: 714, cat: Books, desc: "Книги для малышей и их родителей"}
    - {id: 845, cat: Books, desc: "Комиксы"}
    - {id: 741, cat: Books, desc: "Разное"}
    # Release - groups      # Релиз - группы
    - {id: 378, cat: Other, desc: "Анонсы"}
    # Miscellanea      # Разное
    - {id: 644, cat: Other, desc: "Разное"}

  modes:
    search: [q]
    tv-search: [q, season, ep]
    movie-search: [q]
    music-search: [q]
    book-search: [q]

settings:
  - name: username
    type: text
    label: Username
  - name: password
    type: password
    label: Password
  - name: info_ymd
    type: info
    label: About the date
    default: Before using this indexer please check on the Tapochek website that your account profile <b>Y-m-d</b> setting is <b>Y-m-d H:i</b>. If it is anything else then this indexer will generate a parse error and not return results.
  - name: stripcyrillic
    type: checkbox
    label: Strip Cyrillic Letters
    default: false
  - name: addrussiantotitle
    type: checkbox
    label: Add RUS to end of all titles to improve language detection by Sonarr and Radarr. Will cause English-only results to be misidentified.
    default: false
  - name: freeleech
    type: checkbox
    label: Search freeleech only
    default: false
  - name: sort
    type: select
    label: Sort requested from site
    default: 1
    options:
      1: created
      10: seeders
      7: size
      2: title
  - name: type
    type: select
    label: Order requested from site
    default: 2
    options:
      2: desc
      1: asc
  - name: info_flaresolverr
    type: info_flaresolverr

login:
  path: login.php
  method: form
  form: form[action$="/login.php"]
  captcha:
    type: image
    selector: img[src^="profile.php?mode=confirm&id="]
    input: cfmcd
  inputs:
    login_username: "{{ .Config.username }}"
    login_password: "{{ .Config.password }}"
    autologin: 1
    redirect: index.php
  selectorinputs:
    cookie_test:
      selector: input[name="cookie_test"]
      attribute: value
      optional: true
    confirm_id:
      selector: input[name="confirm_id"]
      attribute: value
      optional: true
    sid:
      selector: input[name="sid"]:not(input[id="dl-sid"])
      attribute: value
      optional: true
  error:
    - selector: h4:contains("Вы ввели")
  test:
    path: index.php
    selector: a[href="./login.php?logout=1"]

search:
  paths:
    # https://tapochek.net/tracker.php?f=-1&gold=1&silver=1&o=1&s=2&tm=-1&sns=-1#results
    - path: tracker.php
  inputs:
    $raw: "{{ if .Categories }}{{ range .Categories }}f[]={{.}}&{{end}}{{ else }}f[]=-1{{ end }}"
    nm: "{{ .Keywords }}"
    o: "{{ .Config.sort }}"
    s: "{{ .Config.type }}"
    tm: -1
    sns: -1
    gold: "{{ if .Config.freeleech }}1{{ else }}{{ end }}"

  keywordsfilters:
    - name: re_replace # S01 to сезон 1
      args: ["(?i)\\bS0*(\\d+)\\b", "сезон $1"]
    - name: re_replace # E02 to сери 1
      args: ["(?i)\\bE0*(\\d+)\\b", "сери $1"]
    - name: re_replace # S01E02 to сезон 1 сери 2
      args: ["(?i)\\bS0*(\\d+)E0*(\\d+)\\b", "сезон $1 сери $2"]

  rows:
    selector: tr[id^="tor_"]:has(a[href^="./download.php?id="])

  fields:
    category:
      selector: td a.gen
      attribute: href
      filters:
        - name: querystring
          args: f
    title:
      selector: a.genmed, a.seedmed
      filters:
        # normalize to SXXEYY format
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*[CС]езоны?.+?(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))?", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*[CС]езоны?.+?(\\d+(?:-\\d+)?)\\s*(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))\\s+из\\s*(\\w?)", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))?", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(\\d+(?:-\\d+)?)\\s*(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))\\s+из\\s*(\\w?)", "S$1E$2 of $3"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)", "S$1E$2"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*[CС]езоны?.+?(\\d+(?:-\\d+)?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "S$1E$2"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?).+?(\\d+(?:-\\d+)?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "S$1E$2"]
        - name: re_replace
          args: ["(?i)[CС]езоны?[\\s:]*(\\d+(?:-\\d+)?)", "S$1"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s+[CС]езоны?", "S$1"]
        - name: re_replace
          args: ["(?i)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)", "E$1 of $2"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s*из\\s*(\\w?)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "E$1 of $2"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s+(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))\\s+из\\s*(\\w?)", "E$1 of $2"]
        - name: re_replace
          args: ["(?i)(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))[\\s:]*(\\d+(?:-\\d+)?)", "E$1"]
        - name: re_replace
          args: ["(?i)(\\d+(?:-\\d+)?)\\s+(?:\\s*(?:[CС]ери[ияй]|Эпизод|Выпуски?))", "E$1"]
        - name: replace
          args: ["Кураж-Бамбей", "kurazh"]
        - name: replace
          args: ["Кубик в Кубе", "Kubik"]
        - name: re_replace
          args: ["(\\([\\p{IsCyrillic}\\W]+\\))|(^[\\p{IsCyrillic}\\W\\d]+\\/ )|([\\p{IsCyrillic} \\-]+,+)|([\\p{IsCyrillic}]+)", "{{ if .Config.stripcyrillic }}{{ else }}$1$2$3$4{{ end }}"]
        - name: re_replace
          args: ["(?i)\\bHDTV[-\\s]?Rip\\b", "HDTV"]
        - name: re_replace
          args: ["(?i)\\bSAT[-\\s]?Rip\\b", "HDTV"]
        - name: re_replace
          args: ["(?i)\\bWEB[-\\s]?DL[-\\s]?Rip\\b", "WEB-DL"]
        - name: re_replace
          args: ["(?i)\\bWEB\\sRip\\b", "WEBRip"]
        - name: re_replace
          args: ["(?i)\\bWEB\\sDL\\b", "WEB-DL"]
        - name: re_replace
          args: ["[\\[\\(\\{<«][\\s\\W]*[\\]\\)\\}>»]", ""]
        - name: re_replace
          args: ["^[\\s&,\\.!\\?\\+\\-_\\|\\/':]+", ""]
        - name: append
          args: "{{ if .Config.addrussiantotitle }} RUS{{ else }}{{ end }}"
    details:
      selector: a.genmed, a.seedmed
      attribute: href
    download:
      selector: a[href^="./download.php?id="]
      attribute: href
    size:
      selector: td:nth-child(6) > u
    date:
      # unix
      selector: td:last-child > u
    seeders:
      selector: td.seedmed > b
    leechers:
      selector: td.leechmed > b
    downloadvolumefactor:
      case:
        img[src="images/tor_gold.gif"]: 0
        img[src="images/tor_silver.gif"]: 0.5
        "*": 1
    uploadvolumefactor:
      text: 1
    minimumratio:
      text: 0.5
    description:
      selector: a.genmed, a.seedmed
# TorrentPier
