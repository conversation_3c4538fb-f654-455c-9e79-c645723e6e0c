---
id: hdtime
name: HDtime
description: "HDtime is a CHINESE Private Torrent Tracker for HD MOVIES / TV / GENERAL"
language: zh-CN
type: private
encoding: UTF-8
links:
  - https://hdtime.org/

caps:
  categorymappings:
    - {id: 401, cat: Movies, desc: "Movies/电影"}
    - {id: 424, cat: Movies/BluRay, desc: "Blu-Ray原盘"}
    - {id: 402, cat: TV, desc: "TV Series/剧集"}
    - {id: 403, cat: TV, desc: "TV Shows/综艺"}
    - {id: 405, cat: TV/Anime, desc: "Animations/动漫"}
    - {id: 414, cat: PC, desc: "Apps/软件"}
    - {id: 407, cat: TV/Sport, desc: "Sports/运体"}
    - {id: 404, cat: TV/Documentary, desc: "Documentaries/纪录片"}
    - {id: 406, cat: Audio/Video, desc: "MusicVideo/音乐MV"}
    - {id: 408, cat: Audio, desc: "Music/音乐"}
    - {id: 410, cat: PC/Games, desc: "Games/游戏"}
    - {id: 411, cat: Books, desc: "Books/文档"}
    - {id: 409, cat: Other, desc: "Misc/其他"}

  modes:
    search: [q]
    tv-search: [q, season, ep, imdbid, doubanid]
    movie-search: [q, imdbid, doubanid]
    music-search: [q]
    book-search: [q]

settings:
  - name: cookie
    type: text
    label: Cookie
  - name: info_cookie
    type: info_cookie
  - name: useragent
    type: text
    label: User-Agent
  - name: info_useragent
    type: info_useragent
  - name: freeleech
    type: checkbox
    label: Search freeleech only
    default: false
  - name: sort
    type: select
    label: Sort requested from site
    default: 4
    options:
      4: created
      7: seeders
      5: size
      1: title
  - name: type
    type: select
    label: Order requested from site
    default: desc
    options:
      desc: desc
      asc: asc
  - name: info_tpp
    type: info
    label: Results Per Page
    default: For best results, change the <b>Torrents per page:</b> setting to <b>100</b> on your account profile.
  - name: info_activity
    type: info
    label: Account Inactivity
    default: "Account retention rules:<ol><li>Veteran User and above will be retained forever</li><li>Elite User and above will not have their account deleted after parking (in the control panel)</li><li>Users with a parked account will be deleted if they do not log in for 400 consecutive days</li><li>Users with a non-parked account will be deleted if they do not log in for 150 consecutive days</li><li>Users with no traffic (ie, upload/download data are both 0) will be deleted if they do not log in for 100 consecutive days.</li></ol>"

login:
  # using cookie method because takelogin.php returns 302
  method: cookie
  inputs:
    cookie: "{{ .Config.cookie }}"
  test:
    path: index.php
    selector: a[href="logout.php"]

search:
  paths:
    - path: torrents.php
  inputs:
    $raw: "{{ range .Categories }}cat{{.}}=1&{{end}}"
    search: "{{ if .Query.IMDBID }}{{ .Query.IMDBID }}{{ else }}{{ end }}{{ if or .Query.IMDBID .Query.DoubanID }} {{ else }}{{ .Keywords }}{{ end }}{{ if .Query.DoubanID }}{{ .Query.DoubanID }}{{ else }}{{ end }}"
    # 0 incldead, 1 active, 2 dead
    incldead: 0
    # 0 all, 1 normal, 2 free, 3 2x, 4 2xfree, 5 50%, 6 2x50%, 7 30%
    spstate: "{{ if .Config.freeleech }}2{{ else }}0{{ end }}"
    # 0 title, 1 descr, 3 uploader, 4 imdburl
    search_area: "{{ if .Query.IMDBID }}4{{ else }}{{ end }}{{ if .Query.DoubanID }}1{{ else }}{{ end }}{{ if or .Query.IMDBID .Query.DoubanID }}{{ else }}0{{ end }}"
    # 0 AND, 1 OR, 2 exact
    search_mode: 0
    sort: "{{ .Config.sort }}"
    type: "{{ .Config.type }}"
    notnewword: 1

  headers:
    User-Agent: ["{{ .Config.useragent }}"]

  rows:
    selector: table.torrents > tbody > tr:has(a[href^="download.php?id="])

  fields:
    category:
      selector: a[href^="?cat="]
      attribute: href
      filters:
        - name: querystring
          args: cat
    title_default:
      selector: a[href^="details.php?id="]
    title:
      selector: a[title][href^="details.php?id="]
      attribute: title
      optional: true
      default: "{{ .Result.title_default }}"
    details:
      selector: a[href^="details.php?id="]
      attribute: href
    download:
      selector: a[href^="download.php?id="]
      attribute: href
    poster:
      selector: img[data-src]
      attribute: data-src
    imdbid:
      # site currently only has a badge and rating, the id is not present. just in case a future update.
      selector: a[href*="imdb.com/title/tt"]
      attribute: href
    doubanid:
      # site currently only has a badge and rating, the id is not present. just in case a future update.
      selector: a[href*="movie.douban.com/subject/"]
      attribute: href
    date_elapsed:
      # time type: time elapsed (default)
      selector: td.rowfollow:nth-child(4) > span[title]
      attribute: title
      optional: true
      filters:
        - name: append
          args: " +08:00" # CST
        - name: dateparse
          args: "yyyy-MM-dd HH:mm:ss zzz"
    date_added:
      # time added
      selector: td.rowfollow:nth-child(4):not(:has(span))
      optional: true
      filters:
        - name: append
          args: " +08:00" # CST
        - name: dateparse
          args: "yyyy-MM-ddHH:mm:ss zzz"
    date:
      text: "{{ if or .Result.date_elapsed .Result.date_added }}{{ or .Result.date_elapsed .Result.date_added }}{{ else }}now{{ end }}"
    size:
      selector: td.rowfollow:nth-child(5)
    seeders:
      selector: td.rowfollow:nth-child(6)
    leechers:
      selector: td.rowfollow:nth-child(7)
    grabs:
      selector: td.rowfollow:nth-child(8)
    downloadvolumefactor:
      case:
        img.pro_free: 0
        img.pro_free2up: 0
        img.pro_50pctdown: 0.5
        img.pro_50pctdown2up: 0.5
        img.pro_30pctdown: 0.3
        "*": 1
    uploadvolumefactor:
      case:
        img.pro_50pctdown2up: 2
        img.pro_free2up: 2
        img.pro_2up: 2
        "*": 1
    minimumratio:
      text: 0.81
    description:
      selector: td.rowfollow:nth-child(2)
      remove: a, b, font, img, span
# NexusPHP v1.8.15 2024-11-23
