---
id: crabpt
name: <PERSON>rabP<PERSON> (蟹黄堡)
description: "CrabP<PERSON> (蟹黄堡) is a CHINESE Private Torrent Tracker for MOVIES / TV / GENERAL"
language: zh-CN
type: private
encoding: UTF-8
requestDelay: 2
links:
  - https://crabpt.vip/

caps:
  # dont forget to update the path categories in the search block
  categorymappings:
    - {id: 401, cat: Movies, desc: "电影 / Movies", default: true}
    - {id: 402, cat: TV, desc: "电视剧 / TVSeries", default: true}
    - {id: 413, cat: TV/Other, desc: "短剧 / Playlet", default: true}
    - {id: 408, cat: Audio, desc: "音乐 / Music", default: true}
    - {id: 405, cat: TV/Anime, desc: "动漫 / Anime", default: true}
    - {id: 406, cat: Audio/Video, desc: "MV", default: true}
    - {id: 403, cat: TV, desc: "综艺 / TV Shows", default: true}
    - {id: 407, cat: TV/Sport, desc: "体育竞技 / Sports", default: true}
    - {id: 404, cat: TV/Documentary, desc: "纪录片 / Documentary", default: true}
    - {id: 409, cat: Other, desc: "其他 / Other", default: true}
    - {id: 415, cat: Books/Comics, desc: "漫画 / Cartoon", default: true}
    - {id: 414, cat: Console, desc: "游戏 / Game", default: true}
    - {id: 412, cat: Other, desc: "学习 / Study", default: true}
    - {id: 411, cat: Audio/Audiobook, desc: "有声书 / Audiobook", default: true}
    - {id: 410, cat: Books/EBook, desc: "电子书 / Ebook", default: true}

  modes:
    search: [q]
    tv-search: [q, season, ep, imdbid, doubanid]
    movie-search: [q, imdbid, doubanid]
    music-search: [q]
    book-search: [q]

settings:
  - name: username
    type: text
    label: Username
  - name: password
    type: password
    label: Password
  - name: 2facode
    type: text
    label: 2FA code
  - name: info_2fa
    type: info
    label: "About 2FA code"
    default: "Only fill in the <b>2FA code</b> box if you have enabled <b>2FA</b> on the CrabPT Web Site. Otherwise just leave it empty."
  - name: freeleech
    type: checkbox
    label: Search freeleech only
    default: false
  - name: sort
    type: select
    label: Sort requested from site
    default: 4
    options:
      4: created
      7: seeders
      5: size
      1: title
  - name: type
    type: select
    label: Order requested from site
    default: desc
    options:
      desc: desc
      asc: asc
  - name: info_tpp
    type: info
    label: Results Per Page
    default: For best results, change the <b>Torrents per page:</b> setting to <b>100</b> on your account profile.
  - name: info_activity
    type: info
    label: Account Inactivity
    default: "Account retention rules:<ol><li>Veteran User and above will be retained forever</li><li>Elite User and above will not have their account deleted after parking (in the control panel)</li><li> Users with a parked account will be deleted if they do not log in for 400 consecutive days</li><li>Users with a non-parked account will be deleted if they do not log in for 150 consecutive days</li><li>Users who have no traffic (i.e. upload/download data are both 0) will be deleted if they do not log in for 100 consecutive days.</li></ol>"

login:
  path: login.php
  method: form
  form: form[action="takelogin.php"]
  captcha:
    type: image
    selector: img[alt="CAPTCHA"]
    input: imagestring
  inputs:
    secret: ""
    username: "{{ .Config.username }}"
    password: "{{ .Config.password }}"
    two_step_code: "{{ .Config.2facode }}"
    logout: ""
    securelogin: ""
    ssl: yes
    trackerssl: yes
  error:
    - selector: td.embedded:has(h2:contains("失败"))
  test:
    path: index.php
    selector: a[href="logout.php"]

search:
  paths:
    - path: torrents.php
      categories: [401, 402, 413, 408, 405, 406, 403, 407, 404, 409]
    - path: special.php
      categories: [415, 414, 412, 411, 410]
  inputs:
    $raw: "{{ range .Categories }}cat{{.}}=1&{{end}}"
    search: "{{ if .Query.IMDBID }}{{ .Query.IMDBID }}{{ else }}{{ end }}{{ if or .Query.IMDBID .Query.DoubanID }} {{ else }}{{ .Keywords }}{{ end }}{{ if .Query.DoubanID }}{{ .Query.DoubanID }}{{ else }}{{ end }}"
    # 0 incldead, 1 active, 2 dead
    incldead: 0
    # 0 all, 1 normal, 2 free, 3 2x, 4 2xfree, 5 50%, 6 2x50%, 7 30%
    spstate: "{{ if .Config.freeleech }}2{{ else }}0{{ end }}"
    # 0 title, 1 descr, 3 uploader, 4 imdburl
    search_area: "{{ if .Query.IMDBID }}4{{ else }}{{ end }}{{ if .Query.DoubanID }}1{{ else }}{{ end }}{{ if or .Query.IMDBID .Query.DoubanID }}{{ else }}0{{ end }}"
    # 0 AND, 1 OR, 2 exact
    search_mode: 0
    sort: "{{ .Config.sort }}"
    type: "{{ .Config.type }}"
    notnewword: 1

  rows:
    selector: table.torrents > tbody > tr:has(a[href^="download.php?id="])

  fields:
    category:
      selector: a[href^="?cat="]
      attribute: href
      filters:
        - name: querystring
          args: cat
    title_default:
      selector: a[href^="details.php?id="]
    title:
      selector: a[title][href^="details.php?id="]
      attribute: title
      optional: true
      default: "{{ .Result.title_default }}"
    details:
      selector: a[href^="details.php?id="]
      attribute: href
    download:
      selector: a[href^="download.php?id="]
      attribute: href
    poster:
      selector: img[data-src]
      attribute: data-src
    imdbid:
      # site currently only has a badge and rating, the id is not present. just in case a future update.
      selector: a[href*="imdb.com/title/tt"]
      attribute: href
    doubanid:
      # site currently only has a badge and rating, the id is not present. just in case a future update.
      selector: a[href*="movie.douban.com/subject/"]
      attribute: href
    date_elapsed:
      # time type: time elapsed (default)
      selector: td.rowfollow:nth-child(4) > span[title]
      attribute: title
      optional: true
      filters:
        - name: append
          args: " +08:00" # CST
        - name: dateparse
          args: "yyyy-MM-dd HH:mm:ss zzz"
    date_added:
      # time added
      selector: td.rowfollow:nth-child(4):not(:has(span))
      optional: true
      filters:
        - name: append
          args: " +08:00" # CST
        - name: dateparse
          args: "yyyy-MM-ddHH:mm:ss zzz"
    date:
      text: "{{ if or .Result.date_elapsed .Result.date_added }}{{ or .Result.date_elapsed .Result.date_added }}{{ else }}now{{ end }}"
    size:
      selector: td.rowfollow:nth-child(5)
      optional: true
      default: 512MB
    seeders:
      selector: td.rowfollow:nth-child(6)
      optional: true
      default: 0
    leechers:
      selector: td.rowfollow:nth-child(7)
      optional: true
      default: 0
    grabs:
      selector: td.rowfollow:nth-child(8)
      optional: true
      default: 0
    downloadvolumefactor:
      case:
        img.pro_free: 0
        img.pro_free2up: 0
        img.pro_50pctdown: 0.5
        img.pro_50pctdown2up: 0.5
        img.pro_30pctdown: 0.3
        "*": 1
    uploadvolumefactor:
      case:
        img.pro_50pctdown2up: 2
        img.pro_free2up: 2
        img.pro_2up: 2
        "*": 1
    minimumratio:
      text: 1.0
    minimumseedtime:
      # 1 day (as seconds = 24 x 60 x 60)
      text: 86400
    description:
      selector: td.rowfollow:nth-child(2)
      remove: a, b, font, img, span
# NexusPHP v1.9.6 2025-06-25
