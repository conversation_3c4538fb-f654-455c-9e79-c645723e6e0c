---
id: torrentqq
name: TorrentQQ
description: "TorrentQ<PERSON> (토렌트큐큐) is a Public KOREAN tracker for Korean media."
language: ko-KR
type: public
encoding: UTF-8
followredirect: true
links:
  - https://torrentqq376.com/
  - https://torrentegg68.com/
legacylinks:
  - https://torrentqq362.com/
  - https://torrentegg54.com/
  - https://torrentqq363.com/
  - https://torrentegg55.com/
  - https://torrentqq364.com/
  - https://torrentegg56.com/
  - https://torrentqq365.com/
  - https://torrentegg57.com/
  - https://torrentqq366.com/
  - https://torrentegg58.com/
  - https://torrentqq367.com/
  - https://torrentegg59.com/
  - https://torrentqq368.com/
  - https://torrentegg60.com/
  - https://torrentqq369.com/
  - https://torrentegg61.com/
  - https://torrentqq370.com/
  - https://torrentegg62.com/
  - https://torrentqq371.com/
  - https://torrentegg63.com/
  - https://torrentqq372.com/
  - https://torrentegg64.com/
  - https://torrentqq373.com/
  - https://torrentegg65.com/
  - https://torrentqq374.com/
  - https://torrentegg66.com/
  - https://torrentqq375.com/
  - https://torrentegg67.com/

caps:
  categorymappings:
    - {id: "adt", cat: XXX, desc: "성인 (XXX)"}
    - {id: "ani", cat: TV/Anime, desc: "애니 (Anime)"}
    - {id: "etc", cat: Other, desc: "기타 (Other)"}
    - {id: "gme", cat: Console, desc: "게임 (Games)"}
    - {id: "med", cat: TV, desc: "방송 (TV)"}
    - {id: "mov", cat: Movies, desc: "영화 (Movie)"}
    - {id: "mus", cat: Audio, desc: "음악 (Music)"}
    - {id: "spo", cat: TV/Sport, desc: "스포츠 (Sport)"}
    - {id: "utl", cat: PC, desc: "유틸 (Software)"}

  modes:
    search: [q]
    tv-search: [q, season, ep]
    movie-search: [q]
    music-search: [q]

settings:
  - name: info_flaresolverr
    type: info_flaresolverr

download:
  infohash:
    hash:
      selector: table.table-bordered > tbody > tr > td > ul > li
      filters:
        - name: regexp
          args: ([A-F|a-f|0-9]{40})
    title:
      selector: table.table-bordered > thead > tr > th > strong
      filters:
        - name: trim
        - name: validfilename

search:
  paths:
    # https://torrentqq76.com/torrent/newest.html
    # https://torrentqq76.com/search?q=cosmic%20sin
    - path: "{{ if .Keywords }}search?q={{ .Keywords }}{{ else }}torrent/newest.html{{ end }}"

  rows:
    selector: ul#searchresult > li:has(a[href$=".html"][title])
    filters:
      - name: andmatch

  fields:
    category:
      selector: a[href$=".html"][title]
      attribute: href
      filters:
        - name: regexp
          args: "\\/torrent\\/(\\w{3})\\/"
    title:
      selector: a[href$=".html"][title]
    details:
      selector: a[href$=".html"][title]
      attribute: href
    download:
      selector: a[href$=".html"][title]
      attribute: href
    date_day:
      selector: div.wr-date:contains("-")
      optional: true
      filters:
        - name: dateparse
          args: "MM-dd"
    date_time:
      selector: div.wr-date:contains(":")
      optional: true
      filters:
        - name: dateparse
          args: "HH:mm"
    date:
      text: "{{ if or .Result.date_time .Result.date_day }}{{ or .Result.date_time .Result.date_day }}{{ else }}now{{ end }}"
    size:
      selector: div.wr-size
      filters:
        - name: append
          args: "B"
    seeders:
      text: 1
    leechers:
      text: 1
    downloadvolumefactor:
      text: 0
    uploadvolumefactor:
      text: 1
# engine n/a
