---
id: magnetcat
name: <PERSON>gnet Cat
description: "Magnet Cat is a Public Tracker for MOVIES / TV / GENERAL"
language: en-US
type: public
encoding: UTF-8
# current domain finder https://魔法猫咪.lol/ or https://哆啦a猫.com/
links:
  - https://magnetcatcat.com/
  - https://clmclm.com/
  - https://www.8800481.xyz
  - https://www.8800483.xyz
  - https://www.8800486.xyz
  - https://www.8800487.xyz
legacylinks:
  - https://www.clm446.buzz/
  - https://www.clm447.buzz/
  - https://www.clm448.buzz/
  - https://www.clm449.buzz/
  - https://www.clm450.buzz/
  - https://www.8800467.xyz/
  - https://www.8800468.xyz/
  - https://www.8800471.xyz/
  - https://www.8800472.xyz/
  - https://www.8800460.xyz/
  - https://www.8800473.xyz/
  - https://www.8800474.xyz/
  - https://www.8800476.xyz/
  - https://www.clm472.sbs/
  - https://www.8800475.xyz
  - https://www.8800477.xyz
  - https://www.8800478.xyz
  - https://www.8800479.xyz
  - https://www.8800484.xyz
  - https://www.8800485.xyz

caps:
  categorymappings:
    - {id: 影视, cat: TV, desc: 影视}
    - {id: 影视, cat: Movies, desc: 影视}
    - {id: 音乐, cat: Audio, desc: 音乐}
    - {id: 图像, cat: Other, desc: 图像}
    - {id: 文档书籍, cat: Books, desc: 文档书籍}
    - {id: 压缩文件, cat: Other, desc: 压缩文件}
    - {id: 安装包, cat: PC, desc: 安装包}
    - {id: 其他, cat: Other, desc: 其他}

  modes:
    search: [q]
    tv-search: [q, season, ep]
    movie-search: [q]
    music-search: [q]
    book-search: [q]

settings:
  - name: cat-id
    type: select
    label: Category
    default: 0
    options:
      0: All categories
      1: Movies and TV shows
      2: Music
      3: Images
      4: Documents and books
      5: Compressed files
      6: Installer packages
      7: Other
  - name: sort
    type: select
    label: Sort requested from site
    default: 2
    options:
      2: created
      1: size
      0: relevance

search:
  paths:
    - path: "search-{{ if .Keywords }}{{ .Keywords }}{{ else }}{{ .Today.Year }}{{ end }}-{{ .Config.cat-id }}-{{ .Config.sort }}-1.html"
  error:
    - selector: :root:contains("Internal Server Error")

  rows:
    selector: div.ssbox

  fields:
    categorydesc:
      selector: div.title > h3 > span
      filters:
        - name: re_replace
          args: ["([\\[\\]]+)", ""]
    title:
      selector: a[href^="/hash/"]
    details:
      selector: a[href^="/hash/"]
      attribute: href
    infohash:
      selector: a[href^="magnet:?xt="]
      attribute: href
      filters:
        - name: regexp
          args: ([A-F|a-f|0-9]{40})
    date:
      selector: div.sbar > span:contains("Date added:") > b, div.sbar > span:contains("添加时间:") > b
      filters:
        - name: append
          args: " +08:00" # CST
        - name: dateparse
          args: "yyyy-MM-dd zzz"
    size:
      selector: div.sbar > span:contains("Size:") > b, div.sbar > span:contains("大小:") > b
    seeders:
      text: 1
    leechers:
      text: 1
    downloadvolumefactor:
      text: 0
    uploadvolumefactor:
      text: 1
# engine n/a
