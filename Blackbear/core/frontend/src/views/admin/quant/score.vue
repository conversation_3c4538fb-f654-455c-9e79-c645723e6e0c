<template>
    <a-card>
        <span slot="extra">
            <TaskCheck class="mr8" :task='27' :time=true />
            <span v-role="['admin.top']">
                <a-button @click="gotoOverview" class="fs12" size="small">速览</a-button>
            </span>
        </span>
        <a-row :gutter="32">
            <a-col :span="6" v-if="quantConfig">
                <a-alert class="mb16" message="总原则：市场因素大于个股基本面，所以市场因素给予高权重。" banner />
                <p>- 基本（{{quantConfig.score1Weight}}）：指数权重获得固定分数，业绩增长加分，因为A股是博弈预期，所以意义不大</p>
                <p>- 技术（{{quantConfig.score2Weight}}）：看趋势给大权重</p>
                <p>- 资金（{{quantConfig.score3Weight}}）：主要看成交量</p>
                <p>- 风口（{{quantConfig.score4Weight}}）：使用个股热度值分数</p>
            </a-col>
            <a-col :span="18">
                <StockTable
                    :pagination="pagination"
                    :height="height-300"
                    :columns="['displayName', 'shortDesc', 'desc2', 'totalscore', 'score']"
                    :extra="true"
                    :dataSource="tableData"
                    :sorter="false"
                    @change="handleTableChange"
                    :source="false" />
            </a-col>
        </a-row>
    </a-card>
</template>

<script>

import moment from 'moment';
import TaskCheck from 'components/TaskCheck'
import StockTable from 'components/StockTable'

export default {
    data() {
        return {
            tableData: [],
            quantConfig: null,
            height: window.bHeight,
            pagination: {
                total: 0,
                current: 1,
                pageSize: 40,
            },
        }
    },
    components: {
        TaskCheck,
        StockTable
    },
    props: {
    },
    computed: {
    },
    created() {},
    watch: {
    },
    destroyed () {
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        fetchData() {
            this.fetchList()
            this.$store.commit('site/showLoading')
            this.$api.black.home({
                key: 'quantConfig'
            }).then((result) => {
                this.tableData = result.stockScore;
                this.quantConfig = result.quantConfig;
            }).catch((err) => {

            }).finally(() => {
                this.$store.commit('site/hideLoading')
            })
        },
        fetchList() {
            this.$api.stock.getScoreList({
                pageSize: this.pagination.pageSize,
                pageIndex: this.pagination.current,
            }).then((result) => {
                this.tableData = result.rows;
                Object.assign(this.pagination, {
                    total: result.count,
                })
            }).catch((err) => {

            }).finally(() => {
                this.$store.commit('site/hideLoading')
            });
        },
        handleTableChange(pagination) {
            this.pagination = pagination;
            this.fetchList()
        },
        gotoOverview() {
            const code_list = []
            this.tableData.forEach(element => {
                code_list.push(element.code)
            });

            this.$router.push({
                name: 'admin-quant-overview',
                query: {
                    list: code_list.join(',')
                }
            });
        },
    },
}
</script>

<style scoped lang="less">

</style>