const TABLE_COLUMNS = [
    {
        title: '标的',
        dataIndex: 'displayName-fixed',
        fixed: true,
        width: 190,
        align: 'center',
        scopedSlots: {
            customRender: 'displayName-fixed'
        },
    },
    {
        title: '标的',
        dataIndex: 'displayName',
        width: 190,
        align: 'center',
        scopedSlots: {
            customRender: 'displayName'
        },
    },
    {
        title: '名称',
        dataIndex: 'f14',
        key: 'f14',
        fixed: true,
        width: 140,
        align: 'center',
        scopedSlots: {
            customRender: 'f14'
        },
    },
    {
        title: '涨速',
        dataIndex: 'f22',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f22'
        },
    },
    {
        title: '成交额',
        dataIndex: 'f6',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f6'
        },
    },
    {
        title: '振幅',
        dataIndex: 'f7',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f7'
        },
    },
    {
        title: '地区',
        dataIndex: 'f102',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f102'
        },
    },
    {
        title: '板块',
        dataIndex: 'f100',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'f100'
        },
    },
    {
        title: '概念',
        dataIndex: 'f103',
        align: 'center',
        width: 200,
        scopedSlots: { 
            customRender: 'f103' 
        },
    },
    {
        title: '核心业务',
        dataIndex: 'keyword',
        align: 'center',
        width: 120,
        scopedSlots: { 
            customRender: 'keyword' 
        },
    },
    {
        title: '短评',
        dataIndex: 'shortDesc',
        align: 'center',
        width: 240,
        scopedSlots: { 
            customRender: 'shortDesc' 
        },
    },
    {
        title: '技术面',
        dataIndex: 'desc2',
        align: 'center',
        width: 240,
        scopedSlots: { 
            customRender: 'desc2' 
        },
    },
    {
        title: '涨跌',
        dataIndex: 'f3',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f3'
        },
    },
    {
        title: '30天收益',
        dataIndex: 'percent',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'percent'
        },
    },
    {
        title: '阶段收益',
        dataIndex: 'decisionPercent',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'decisionPercent'
        },
    },
    {
        title: '总收益',
        dataIndex: 'ret',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'ret'
        },
    },
    {
        title: 'beta',
        dataIndex: 'beta',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'beta'
        },
    },
    {
        title: 'alpha',
        dataIndex: 'alpha',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'alpha'
        },
    },
    {
        title: 'ROE',
        dataIndex: 'roe',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'roe'
        },
    },
    {
        title: 'PEG',
        dataIndex: 'peg',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'peg'
        },
    },
    {
        title: '回撤',
        dataIndex: 'md',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'md'
        },
    },
    {
        title: '归母净利润同比增长率',
        dataIndex: 'yoypni',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'yoypni'
        },
    },
    {
        title: '3日',
        dataIndex: 'f127',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f127'
        },
    },
    {
        title: '6日',
        dataIndex: 'f149',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f149'
        },
    },
    {
        title: '近60日涨跌幅',
        dataIndex: 'f24',
        align: 'center',
        width: 140,
        scopedSlots: {
            customRender: 'f24'
        },
    },
    {
        title: '年初至今涨跌幅',
        dataIndex: 'f25',
        align: 'center',
        width: 160,
        scopedSlots: {
            customRender: 'f25'
        },
    },
    {
        title: '决断日涨幅',
        dataIndex: 'decisionPercent',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'decisionPercent'
        },
    },
    {
        title: '底部涨幅',
        dataIndex: 'ret100',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'ret100'
        },
    },
    {
        title: '现价',
        dataIndex: 'f2',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'f2'
        },
    },
    {
        title: '总市值',
        dataIndex: 'f20',
        filtered: true,
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'f20'
        }
    },
    {
        title: '概念',
        dataIndex: 'tag',
        align: 'center',
        width: 180,
        scopedSlots: {
            customRender: 'tag'
        },
    },
    {
        title: '标记',
        dataIndex: 'tags',
        align: 'center',
        width: 120,
        scopedSlots: {
            customRender: 'tags'
        },
    },
    {
        title: '连板数',
        dataIndex: 'limit',
        align: 'center',
        width: 180,
        scopedSlots: {
            customRender: 'limit'
        },
    },
    {
        title: '星级',
        dataIndex: 'star',
        align: 'center',
        width: 180,
        scopedSlots: {
            customRender: 'star'
        },
    },
    {
        title: '市盈率',
        dataIndex: 'f9',
        align: 'center',
        width: 100,
    },
    {
        title: '市净率',
        dataIndex: 'f23',
        align: 'center',
        width: 100,
    },
    {
        title: '录入原因',
        dataIndex: 'reason',
        align: 'center',
        width: 200,
    },
    {
        title: '录入日期',
        dataIndex: 'date',
        align: 'center',
        width: 100,
    },
    {
        title: '收益率',
        dataIndex: 'rate',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'rate'
        },
    },
    {
        title: '形态',
        dataIndex: 'trend',
        align: 'left',
        width: 200,
        scopedSlots: {
            customRender: 'trend'
        },
    },
    {
        title: '形态',
        dataIndex: 'kline',
        align: 'left',
        width: 200,
        scopedSlots: {
            customRender: 'kline'
        },
    },
    {
        title: '今日港资占总股本比',
        dataIndex: 'zzb_one',
        align: 'center',
        width: 200,
        scopedSlots: {
            customRender: 'zzb_one'
        },
    },
    {
        title: '业绩',
        dataIndex: 'finance',
        align: 'center',
        width: 280,
        scopedSlots: {
            customRender: 'finance'
        },
    },
    {
        title: '涨跌幅',
        dataIndex: 'change',
        align: 'center',
        width: 360,
        scopedSlots: {
            customRender: 'change'
        },
    },
    {
        title: '加权总分',
        dataIndex: 'totalscore',
        align: 'center',
        width: 100,
        scopedSlots: {
            customRender: 'totalscore'
        },
    },
    {
        title: '四维评分系统',
        dataIndex: 'score',
        align: 'center',
        width: 200,
        scopedSlots: {
            customRender: 'score'
        },
    }
]

const TABLE_EXTRA_COLUMNS = [
    {
        title: '涨跌幅',
        dataIndex: 'change',
        align: 'center',
        scopedSlots: {
            customRender: 'change'
        },
    },
    {
        title: '业绩',
        dataIndex: 'finance',
        align: 'center',
        scopedSlots: {
            customRender: 'finance'
        },
    },
    {
        title: '标签',
        dataIndex: 'alltags',
        align: 'center',
        scopedSlots: {
            customRender: 'alltags'
        },
    },
]



export default {
    TABLE_COLUMNS,
    TABLE_EXTRA_COLUMNS,
}