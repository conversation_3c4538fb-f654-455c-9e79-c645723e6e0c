# -*- coding: UTF-8 -*-
"""
全股票量化分析任务（企业级优化版）

主要功能:
1. 多线程并发获取股票历史数据
2. 计算技术指标（30天最高/最低价、三天涨跌趋势）
3. 数据清洗和存储到数据库
4. 支持增量更新和全量更新模式

优化特性:
- 面向对象设计，提升代码可维护性
- 完善的错误处理和重试机制
- 详细的日志记录和性能监控
- 数据验证和质量保证
- 资源自动管理和清理
"""

import sys
import os
import threading
import json
import time
import argparse
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from contextlib import contextmanager

import pandas as pd
import redis
from tqdm import tqdm
from sqlalchemy.types import String, Float
from sqlalchemy import and_

# 自动注入项目根目录
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 数据库与工具类
from db.database import DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.logger import logger
from utils.helper import ago_day_timestr, sz_or_sh, check_bool
from db.fetch import fetch_all_stock, quant_monitor
from config.settings import get_settings


# 常量定义
TASK_QUEUE_NAME = 'stock_data_queue'
MAX_RETRY_COUNT = 3
DEFAULT_LOOKBACK_DAYS = 125
REDIS_DB_INDEX = 7

# 数据库字段类型映射
DATABASE_DTYPE_MAPPING = {
    'code': String(6),
    'date': String(10),
    'tradedate': String(8),
    'open': Float,
    'high': Float,
    'low': Float,
    'close': Float,
    'volume': Float,
    'amount': Float,
    'turn': Float,
    'pctChg': Float,
    'peTTM': Float,
    'pbMRQ': Float,
    'max': String(1),
    'min': String(1),
    'rose3': String(1),
    'fall3': String(1),
    'ret30': Float,
}


@dataclass
class TaskConfig:
    """任务配置数据类"""
    stock_code: str
    start_date: str
    end_date: str
    onlyend: bool = False
    fail_count: int = 0


class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化Redis客户端"""
        try:
            settings = get_settings()
            self.client = redis.Redis(
                host=getattr(settings, 'redis_host', os.getenv('BB_REDIS_HOST')),
                port=getattr(settings, 'redis_port', os.getenv('BB_REDIS_PORT')),
                db=REDIS_DB_INDEX,
                password=getattr(settings, 'redis_password', os.getenv('BB_REDIS_PASSWORD'))
            )
            # 测试连接
            self.client.ping()
            logger.info("Redis连接初始化成功")
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {e}")
            raise
    
    @contextmanager
    def get_client(self):
        """获取Redis客户端的上下文管理器"""
        try:
            yield self.client
        except Exception as e:
            logger.error(f"Redis操作失败: {e}")
            raise
    
    def close(self):
        """关闭Redis连接"""
        if self.client:
            self.client.close()
            logger.info("Redis连接已关闭")


class TechnicalIndicatorCalculator:
    """技术指标计算器"""
    
    @staticmethod
    def add_row_index(df: pd.DataFrame) -> pd.DataFrame:
        """添加行索引"""
        df['rowIndex'] = df.index
        return df
    
    @staticmethod
    def generate_record_id(df: pd.DataFrame) -> pd.DataFrame:
        """生成记录唯一ID"""
        def _generate_id(row):
            raw_string = f"{row['code']}{row['tradedate']}"
            return hashlib.md5(raw_string.encode("utf-8")).hexdigest().lower().replace('-', '')
        
        df['id'] = df.apply(_generate_id, axis=1)
        return df
    
    @staticmethod
    def is_30day_max_price(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为30天最高价"""
        try:
            if row['rowIndex'] < 29 or len(df) <= 30:
                return '0'
            
            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_30 = rows_before_current.iloc[-30:]
            max_30 = df_30['close'].max()
            
            return '1' if row['close'] == max_30 else '0'
        except Exception as e:
            logger.warning(f"计算30天最高价失败: {e}")
            return '0'
    
    @staticmethod
    def is_30day_min_price(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为30天最低价"""
        try:
            if row['rowIndex'] < 29 or len(df) <= 30:
                return '0'
            
            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_30 = rows_before_current.iloc[-30:]
            min_30 = df_30['close'].min()
            
            return '1' if row['close'] == min_30 else '0'
        except Exception as e:
            logger.warning(f"计算30天最低价失败: {e}")
            return '0'
    
    @staticmethod
    def is_three_day_rise(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为三天连续上涨"""
        try:
            if row['rowIndex'] < 2 or len(df) < 3:
                return '0'
            
            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_3 = rows_before_current.iloc[-3:]
            
            all_positive = all(df_3['pctChg'] > 0)
            return '1' if all_positive else '0'
        except Exception as e:
            logger.warning(f"计算三天上涨失败: {e}")
            return '0'
    
    @staticmethod
    def is_three_day_fall(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为三天连续下跌"""
        try:
            if row['rowIndex'] < 2 or len(df) < 3:
                return '0'
            
            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_3 = rows_before_current.iloc[-3:]
            
            all_negative = all(df_3['pctChg'] < 0)
            return '1' if all_negative else '0'
        except Exception as e:
            logger.warning(f"计算三天下跌失败: {e}")
            return '0'
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        try:
            logger.debug(f"开始计算技术指标，数据量: {len(df)}")
            
            # 添加行索引
            df = self.add_row_index(df)
            
            # 生成记录ID
            df = self.generate_record_id(df)
            
            # 计算技术指标
            df['max'] = df.apply(lambda x: self.is_30day_max_price(df, x), axis=1)
            df['min'] = df.apply(lambda x: self.is_30day_min_price(df, x), axis=1)
            df['rose3'] = df.apply(lambda x: self.is_three_day_rise(df, x), axis=1)
            df['fall3'] = df.apply(lambda x: self.is_three_day_fall(df, x), axis=1)
            
            # 清理临时列
            df = df.drop(columns=['rowIndex'])
            
            # 清理空字符串
            df = df.replace(r'^\s*$', 0, regex=True)
            
            logger.debug(f"技术指标计算完成，最终数据量: {len(df)}")
            return df
            
        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            raise


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.akshare = Akshare()
        self.baostock = Baostock()
        self.indicator_calculator = TechnicalIndicatorCalculator()
    
    def fetch_data_with_akshare(self, task_config: TaskConfig) -> pd.DataFrame:
        """使用 Akshare 获取数据（备用方法）"""
        try:
            logger.info(f"使用 Akshare 获取股票 {task_config.stock_code} 数据")
            return self.akshare.query_history_data(
                task_config.stock_code, 
                task_config.start_date, 
                task_config.end_date
            )
        except Exception as e:
            logger.warning(f"Akshare 获取股票 {task_config.stock_code} 数据失败: {e}")
            return pd.DataFrame()
    
    def fetch_data_with_baostock(self, task_config: TaskConfig) -> pd.DataFrame:
        """使用 Baostock 获取数据（主要方法）"""
        try:
            logger.info(f"使用 Baostock 获取股票 {task_config.stock_code} 数据")
            
            # 格式化日期
            start_date_formatted = datetime.strptime(task_config.start_date, '%Y%m%d').strftime('%Y-%m-%d')
            end_date_formatted = datetime.strptime(task_config.end_date, '%Y%m%d').strftime('%Y-%m-%d')
            
            return self.baostock.query_history_data(
                sz_or_sh(task_config.stock_code), 
                start_date_formatted, 
                end_date_formatted
            )
        except Exception as e:
            logger.warning(f"Baostock 获取股票 {task_config.stock_code} 数据失败: {e}")
            return pd.DataFrame()
