# -*- coding: UTF-8 -*-
"""
股票数据查询系统 (优化版本)

0 15 18 * * 1-5

主要功能:
1. 股票历史数据获取和存储
2. 技术指标计算 (30日最高/最低价、三连涨/跌等)
3. 股票代码同步和维护
4. 多线程并发数据处理
5. Redis任务队列管理

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 资源管理和连接池
- 模块化设计和类型提示
"""

import sys
import os
from pathlib import Path
# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import redis
import json
import time
import pandas as pd
import os
import hashlib
import argparse
import threading  # <-- 1. 导入threading模块
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed, CancelledError

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode
from sqlalchemy.types import String, Float, Integer, Boolean
from sqlalchemy import and_

# 工具类
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.helper import ago_day_timestr, sz_or_sh, check_bool
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 数据获取
from db.fetch import fetch_all_stock, quant_monitor, fetch_all_history, fetch_all_stock_codes

# 第三方库
import akshare as ak
from tqdm import tqdm

@dataclass
class StockTask:
    """股票数据任务"""
    stock_code: str
    start_date: str
    end_date: str
    onlyend: bool = False
    fail_count: int = 0

@dataclass
class TechnicalIndicators:
    """技术指标数据类"""
    max_30: str = '0'
    min_30: str = '0'
    rose3: str = '0'
    fall3: str = '0'

class StockDataProcessor:
    """股票数据处理器"""

    def __init__(self):
        self.settings = get_settings()
        self.akshare = Akshare()
        self.bs = Baostock()
        self.redis_client = self._init_redis()
        self.db_lock = threading.Lock() # <-- 2. 初始化数据库写入锁

        self.dtypedict = {
            'code': String(6), 'date': String(10), 'tradedate': String(8), 'open': Float, 'high': Float,
            'low': Float, 'close': Float, 'volume': Float, 'amount': Float, 'turn': Float,
            'pctChg': Float, 'peTTM': Float, 'pbMRQ': Float, 'max': String(1), 'min': String(1),
            'rose3': String(1), 'fall3': String(1), 'ret30': Float,
        }

    def __enter__(self):
        logger.debug("StockDataProcessor 上下文管理器进入")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        logger.debug("开始清理 StockDataProcessor 资源...")
        if hasattr(self.bs, 'logout'):
            try:
                self.bs.logout()
                logger.debug("Baostock连接已关闭")
            except Exception as e:
                logger.warning(f"关闭Baostock连接失败: {e}")
        if self.redis_client:
            try:
                self.redis_client.close()
                logger.debug("Redis连接已关闭")
            except Exception as e:
                logger.warning(f"关闭Redis连接失败: {e}")
        logger.debug("StockDataProcessor 资源清理完成")

    def _init_redis(self) -> Optional[redis.Redis]:
        try:
            redis_client = redis.Redis(
                host=os.getenv('BB_REDIS_HOST', 'localhost'), port=int(os.getenv('BB_REDIS_PORT', 6379)),
                db=7, password=os.getenv('BB_REDIS_PASSWORD'), decode_responses=True
            )
            redis_client.ping()
            logger.info("Redis连接成功")
            return redis_client
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return None

    # 省略了没有改动的静态方法 to save space ...
    @staticmethod
    def is_max_close_price(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为30天最高价"""
        try:
            if row['rowIndex'] < 29 or len(df) <= 30: return '0'
            df_30 = df.iloc[row['rowIndex'] - 29 : row['rowIndex'] + 1]
            return '1' if row['close'] == df_30['close'].max() else '0'
        except Exception as e:
            logger.error(f"计算30天最高价失败: {e}"); return '0'

    @staticmethod
    def is_min_close_price(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为30天最低价"""
        try:
            if row['rowIndex'] < 29 or len(df) <= 30: return '0'
            df_30 = df.iloc[row['rowIndex'] - 29 : row['rowIndex'] + 1]
            return '1' if row['close'] == df_30['close'].min() else '0'
        except Exception as e:
            logger.error(f"计算30天最低价失败: {e}"); return '0'

    @staticmethod
    def is_three_day_rose(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为三连涨"""
        try:
            if row['rowIndex'] < 2 or len(df) < 3: return '0'
            df_3 = df.iloc[row['rowIndex'] - 2 : row['rowIndex'] + 1]
            return '1' if (df_3['pctChg'] > 0).all() else '0'
        except Exception as e:
            logger.error(f"计算三连涨失败: {e}"); return '0'

    @staticmethod
    def is_three_day_fall(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为三连跌"""
        try:
            if row['rowIndex'] < 2 or len(df) < 3: return '0'
            df_3 = df.iloc[row['rowIndex'] - 2 : row['rowIndex'] + 1]
            return '1' if (df_3['pctChg'] < 0).all() else '0'
        except Exception as e:
            logger.error(f"计算三连跌失败: {e}"); return '0'
    
    # ... 省略结束

    def get_stock_data_method1(self, task: StockTask) -> Optional[pd.DataFrame]:
        max_retries = 3
        retry_delay = 2
        for attempt in range(max_retries):
            try:
                return self.akshare.query_history_data(task.stock_code, task.start_date, task.end_date)
            except Exception as e:
                logger.warning(f"Method1获取数据失败 {task.stock_code} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1: time.sleep(retry_delay); retry_delay *= 2
                else: logger.error(f"Method1最终失败 {task.stock_code}: {e}"); return None

    def get_stock_data_method2(self, task: StockTask) -> Optional[pd.DataFrame]:
        max_retries = 3
        retry_delay = 2
        for attempt in range(max_retries):
            try:
                start_date = datetime.strptime(task.start_date, '%Y%m%d').strftime('%Y-%m-%d')
                end_date = datetime.strptime(task.end_date, '%Y%m%d').strftime('%Y-%m-%d')
                return self.bs.query_history_data(sz_or_sh(task.stock_code), start_date, end_date)
            except Exception as e:
                logger.warning(f"Method2获取数据失败 {task.stock_code} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1: time.sleep(retry_delay); retry_delay *= 2
                else: logger.error(f"Method2最终失败 {task.stock_code}: {e}"); return None

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        try:
            if df.empty: return df
            df['rowIndex'] = range(len(df))
            df['id'] = df.apply(lambda x: hashlib.md5((str(x['code']) + str(x['tradedate'])).encode("utf-8")).hexdigest(), axis=1)
            df['max'] = df.apply(lambda x: self.is_max_close_price(df, x), axis=1)
            df['min'] = df.apply(lambda x: self.is_min_close_price(df, x), axis=1)
            df['rose3'] = df.apply(lambda x: self.is_three_day_rose(df, x), axis=1)
            df['fall3'] = df.apply(lambda x: self.is_three_day_fall(df, x), axis=1)
            df = df.drop(columns=['rowIndex']).replace(r'^\s*$', 0, regex=True)
            return df
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}", exc_info=True)
            return pd.DataFrame()

    def save_stock_data(self, df: pd.DataFrame, task: StockTask) -> bool:
        """保存股票数据到数据库（线程安全）"""
        # <-- 3. 使用锁来确保数据库写入操作是串行的，防止死锁
        with self.db_lock:
            try:
                if df.empty: return True # 无数据也算成功，不重试
                df_to_save = df.copy()
                with db_manager.get_session() as session:
                    if task.onlyend:
                        df_to_save = df_to_save[df_to_save['tradedate'] == task.end_date]
                        if not df_to_save.empty:
                            session.query(StockHistory).filter(and_(StockHistory.tradedate == task.end_date, StockHistory.code == task.stock_code)).delete(synchronize_session=False)
                    else:
                        trade_dates = df_to_save['tradedate'].unique().tolist()
                        if trade_dates:
                            session.query(StockHistory).filter(and_(StockHistory.code == task.stock_code, StockHistory.tradedate.in_(trade_dates))).delete(synchronize_session=False)
                    session.commit()
                if not df_to_save.empty:
                    df_to_save.to_sql('stockhistory', con=engine, if_exists='append', index=False, dtype=self.dtypedict, method='multi')
                return True
            except Exception as e:
                logger.error(f"保存股票 {task.stock_code} 数据时发生加锁下异常: {e}", exc_info=True)
                return False

    @monitor_performance
    def process_stock_task(self, task: StockTask) -> bool:
        try:
            df = self.get_stock_data_method1(task)
            if df is None or df.empty:
                logger.warning(f"Method1失败，尝试Method2: {task.stock_code}")
                df = self.get_stock_data_method2(task)
            if df is None or df.empty:
                logger.error(f"所有方法都获取不到数据: {task.stock_code}")
                return False
            df = self.calculate_technical_indicators(df)
            return self.save_stock_data(df, task)
        except Exception as e:
            logger.error(f"处理股票任务 {task.stock_code} 失败: {e}", exc_info=True)
            return False

    @monitor_performance
    def batch_process_stocks(self, stock_codes: List[str], start_date: str, end_date: str, onlyend: bool = False, max_workers: int = 10, start_time: float = None) -> bool:
        logger.info(f"开始批量处理 {len(stock_codes)} 只股票，最大并发数: {max_workers}")
        tasks = [StockTask(code, start_date, end_date, onlyend) for code in stock_codes]
        success_count = 0
        failed_tasks = []
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_task = {executor.submit(self.process_stock_task, task): task for task in tasks}
                for future in tqdm(as_completed(future_to_task), total=len(tasks), desc="处理股票"):
                    # TODO: 暂时解决程序不退出的问题，检查是否超时
                    if start_time is not None and time.time() - start_time > 3600:
                        logger.critical("程序运行超过1小时，自动退出")
                        os._exit(1)
                    if is_exit_requested():
                        logger.info("检测到退出信号，取消剩余任务...")
                        for f in future_to_task:
                            if not f.done(): f.cancel()
                        break
                    task = future_to_task[future]
                    try:
                        if future.result(timeout=300):
                            success_count += 1
                        else:
                            failed_tasks.append(task)
                    except CancelledError:
                        logger.debug(f"任务 {task.stock_code} 已取消")
                    except Exception as e:
                        logger.error(f"任务 {task.stock_code} 执行异常: {e}", exc_info=True)
                        failed_tasks.append(task)
            if failed_tasks:
                logger.warning(f"{len(failed_tasks)} 个任务处理失败: {[t.stock_code for t in failed_tasks]}")
            logger.info(f"批量处理完成: 成功 {success_count}/{len(stock_codes)} 只股票")
            return len(failed_tasks) == 0
        except Exception as e:
            logger.error(f"批量处理过程中发生严重错误: {e}", exc_info=True)
            return False

    @monitor_performance
    def sync_stock_codes(self) -> bool:
        try:
            logger.info("开始同步股票代码")
            stock_info = ak.stock_info_a_code_name()
            if stock_info.empty: logger.error("获取股票代码列表失败"); return False
            with db_manager.get_session() as session:
                session.query(StockCode).delete()
                session.bulk_insert_mappings(StockCode, stock_info.to_dict('records'))
                session.commit()
            logger.info(f"股票代码同步完成: {len(stock_info)} 只股票")
            return True
        except Exception as e:
            logger.error(f"同步股票代码失败: {e}", exc_info=True)
            return False

@monitor_performance
def main():
    logger.info("🚀 开始股票数据查询系统 (优化版本)")
    start_time = time.time()  # 记录启动时间
    try:
        parser = argparse.ArgumentParser()
        parser.add_argument("-b", "--begin", type=str)
        parser.add_argument("-e", "--end", type=str)
        parser.add_argument("-o", "--onlyend", type=check_bool, nargs='?', const=True, default=True)
        parser.add_argument("-s", "--sync", type=check_bool, nargs='?', const=True, default=True)
        parser.add_argument("-w", "--workers", type=int, default=10) # 适当增加worker数量
        args = parser.parse_args()
        
        begin_date = args.begin or ago_day_timestr(30, '%Y%m%d')
        end_date = args.end or time.strftime('%Y%m%d', time.localtime())
        logger.info(f"执行参数: 开始={begin_date}, 结束={end_date}, 仅最后一天={args.onlyend}, 同步代码={args.sync}, 工作线程={args.workers}")

        with StockDataProcessor() as processor:
            overall_success = True
            if args.sync and not processor.sync_stock_codes():
                overall_success = False
            stock_codes = fetch_all_stock(mark=False)
            if not stock_codes:
                logger.error("未获取到任何股票代码，任务终止"); quant_monitor(9, False); return False
            if not processor.batch_process_stocks(stock_codes, begin_date, end_date, args.onlyend, args.workers, start_time):
                overall_success = False
        
        quant_monitor(9, overall_success)
        logger.info(f"✅ 股票数据查询系统执行{'成功' if overall_success else '中有失败项'}")
        return overall_success
    except KeyboardInterrupt:
        logger.info("收到中断信号，优雅退出..."); quant_monitor(9, False); return False
    except Exception as e:
        logger.error(f"程序发生未捕获的严重错误: {e}", exc_info=True); quant_monitor(9, False); return False

if __name__ == '__main__':
    setup_signal_handler()
    try:
        is_successful = main()
        sys.exit(0 if is_successful else 1)
    except SystemExit as e:
        sys.exit(e.code)
    except KeyboardInterrupt:
        logger.info("程序被用户强制中断。"); sys.exit(1)
    except Exception as e:
        logger.critical(f"程序顶层异常退出: {e}", exc_info=True); sys.exit(1)