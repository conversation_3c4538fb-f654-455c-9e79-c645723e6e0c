# -*- coding: UTF-8 -*-
"""
股票概览分析系统 (优化版本)

0 0 20 * * 1-5

主要功能:
1. 生成各类股票筛选列表
2. 成分股分类管理 (上证50、创业板50、科创50等)
3. 收益率排行榜生成
4. 量化策略股票池构建
5. 技术指标筛选

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import argparse
import time
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Amount, Source, Stock, QuantList

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 数据获取
from db.fetch import is_trade_date, quant_monitor, fetch_all_stock
from sqlalchemy.dialects.mysql import insert

@dataclass
class StockListConfig:
    """股票列表配置"""
    name: str
    sql: str
    desc: str
    limit: int = 4000

@dataclass
class StockListResult:
    """股票列表结果"""
    name: str
    desc: str
    codes: List[str]
    count: int

class StockOverviewProcessor:
    """股票概览分析处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 预定义的股票筛选配置
        self.stock_list_configs = self._init_stock_list_configs()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def _init_stock_list_configs(self) -> List[StockListConfig]:
        """
        初始化股票列表配置

        :return: 股票列表配置列表
        """
        configs = [
            # 重点观察股票
            StockListConfig(
                name='每日重点观察个股',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE star = true
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='趋势型个股,每日8点更新'
            ),

            # 成分股系列
            StockListConfig(
                name='[成分股]上证50',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%上证50%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='上证50成分股'
            ),

            StockListConfig(
                name='[成分股]创业板50',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%创业板50%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='创业板50成分股'
            ),

            StockListConfig(
                name='[成分股]科创50',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%科创50%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='科创50成分股'
            ),

            StockListConfig(
                name='[成分股]专精特新',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%专精特新%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='专精特新企业'
            ),

            StockListConfig(
                name='[成分股]沪深300',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%沪深300%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='沪深300成分股'
            ),

            StockListConfig(
                name='[成分股]中证500',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%中证500%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='中证500成分股'
            ),

            StockListConfig(
                name='[成分股]中证1000',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE tags LIKE '%中证1000%'
                    ORDER BY decisionPercent DESC
                    LIMIT 4000
                ''',
                desc='中证1000成分股'
            ),

            # 收益率系列
            StockListConfig(
                name='[收益]阶段收益率',
                sql='''
                    SELECT code, decisionPercent
                    FROM stock
                    WHERE decisionPercent > 0
                    ORDER BY decisionPercent DESC
                    LIMIT 300
                ''',
                desc='市值与成交额达到量化标准'
            ),

            StockListConfig(
                name='[量化]涨停连板',
                sql='''
                    SELECT code, `limit`
                    FROM stock
                    WHERE `limit` > 0
                    ORDER BY `limit` DESC
                ''',
                desc='涨停连板股票'
            ),

            StockListConfig(
                name='[收益]最低点到目前的收益率',
                sql='''
                    SELECT code, ret100
                    FROM stock
                    WHERE ret100 > 0
                    ORDER BY ret100 DESC
                    LIMIT 300
                ''',
                desc='从最低点的收益率排行'
            ),

            StockListConfig(
                name='[收益]CAPM模型收益率',
                sql='''
                    SELECT code, ret
                    FROM stock
                    WHERE ret > 0
                    ORDER BY ret DESC
                    LIMIT 300
                ''',
                desc='CAPM模型计算的收益率'
            ),

            StockListConfig(
                name='[成分股]星标',
                sql='''
                    SELECT code, ret
                    FROM stock
                    WHERE rate > 0
                ''',
                desc='星标股票'
            ),

            # 技术指标系列
            StockListConfig(
                name='30日新高，收盘价高于所有盘中最高价',
                sql='''
                    SELECT code, max30, ret
                    FROM stock
                    WHERE max30 = true
                    ORDER BY ret DESC
                ''',
                desc='30日新高技术突破'
            )
        ]

        return configs

    def execute_stock_query(self, config: StockListConfig) -> Optional[StockListResult]:
        """
        执行股票查询

        :param config: 股票列表配置
        :return: 股票列表结果
        """
        try:
            logger.debug(f"执行查询: {config.name}")

            with engine.connect() as conn:
                # 使用text()包装SQL以避免格式化问题
                from sqlalchemy import text
                result = conn.execute(text(config.sql))
                codes = []

                for row in result:
                    # 兼容不同的行访问方式
                    if hasattr(row, '_mapping'):
                        # SQLAlchemy 1.4+ 的方式
                        row_dict = row._mapping
                    else:
                        # 旧版本的方式
                        row_dict = dict(row)

                    if 'code' in row_dict:
                        codes.append(str(row_dict['code']))

                stock_result = StockListResult(
                    name=config.name,
                    desc=config.desc,
                    codes=codes,
                    count=len(codes)
                )

                logger.debug(f"查询 {config.name} 完成: 找到 {len(codes)} 只股票")
                return stock_result

        except Exception as e:
            logger.error(f"执行查询 {config.name} 失败: {e}")
            return None

    def save_stock_list(self, result: StockListResult) -> bool:
        """
        保存股票列表到数据库

        :param result: 股票列表结果
        :return: 是否成功
        """
        try:
            if not result.codes:
                logger.warning(f"股票列表 {result.name} 为空，跳过保存")
                return True

            with db_manager.get_session() as session:
                item = {
                    'name': result.name,
                    'desc': result.desc,
                    'list': ','.join(result.codes)
                }

                insert_stmt = insert(QuantList).values(**item)
                on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)

                session.execute(on_duplicate_key_stmt)
                session.commit()

            logger.debug(f"股票列表 {result.name} 保存成功: {result.count} 只股票")
            return True

        except Exception as e:
            logger.error(f"保存股票列表 {result.name} 失败: {e}")
            return False

    def calculate_profit_growth_stocks(self) -> Optional[StockListResult]:
        """
        计算扣非净利润增长股票

        :return: 股票列表结果
        """
        try:
            logger.info("开始计算扣非净利润增长股票")

            with db_manager.get_session() as session:
                stocks = session.query(Stock).yield_per(1000)
                code_list = []

                for stock in stocks:
                    try:
                        if not stock.profit:
                            continue

                        profit_arr = stock.profit.split(',')
                        arr_len = len(profit_arr)

                        if arr_len < 5:
                            continue

                        # 计算环比和同比增长
                        current = float(profit_arr[arr_len - 1])
                        prev_quarter = float(profit_arr[arr_len - 2])
                        prev_year = float(profit_arr[arr_len - 5])

                        if prev_quarter == 0 or prev_year == 0:
                            continue

                        quarter_growth = (current - prev_quarter) / prev_quarter
                        year_growth = (current - prev_year) / prev_year

                        # 环比增长20%，同比增长50%
                        if quarter_growth > 0.2 and year_growth > 0.5:
                            code_list.append(stock.code)

                    except (ValueError, IndexError, AttributeError):
                        continue

                result = StockListResult(
                    name='扣非净利润增长',
                    desc='扣非净利润环比大增20%，同比大增50%',
                    codes=code_list,
                    count=len(code_list)
                )

                logger.info(f"扣非净利润增长股票计算完成: {len(code_list)} 只股票")
                return result

        except Exception as e:
            logger.error(f"计算扣非净利润增长股票失败: {e}")
            return None

    @monitor_performance
    def process_all_stock_lists(self, include_profit_growth: bool = False) -> bool:
        """
        处理所有股票列表

        :param include_profit_growth: 是否包含利润增长计算
        :return: 是否成功
        """
        try:
            logger.info("开始处理所有股票列表")

            success_count = 0
            total_count = len(self.stock_list_configs)

            # 处理预定义的股票列表
            for config in self.stock_list_configs:
                try:
                    # 执行查询
                    result = self.execute_stock_query(config)
                    if result is None:
                        continue

                    # 保存结果
                    if self.save_stock_list(result):
                        success_count += 1
                        logger.info(f"✅ {config.name}: {result.count} 只股票")
                    else:
                        logger.error(f"❌ {config.name}: 保存失败")

                except Exception as e:
                    logger.error(f"处理股票列表 {config.name} 失败: {e}")
                    continue

            # 处理扣非净利润增长股票 (可选)
            if include_profit_growth:
                try:
                    profit_result = self.calculate_profit_growth_stocks()
                    if profit_result and self.save_stock_list(profit_result):
                        success_count += 1
                        total_count += 1
                        logger.info(f"✅ {profit_result.name}: {profit_result.count} 只股票")
                except Exception as e:
                    logger.error(f"处理扣非净利润增长股票失败: {e}")

            logger.info(f"股票列表处理完成: 成功 {success_count}/{total_count} 个列表")
            return success_count > 0

        except Exception as e:
            logger.error(f"处理所有股票列表失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def generate_stock_overview(include_profit_growth: bool = False) -> bool:
    """生成股票概览 - 优化版本"""
    with StockOverviewProcessor() as processor:
        return processor.process_all_stock_lists(include_profit_growth)

@monitor_performance
def main():
    """
    主函数 - 执行股票概览分析系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始股票概览分析系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="股票概览分析系统 (优化版本)")
        arg_parser.add_argument("-p", "--profit", required=False, type=check_bool,
                               nargs='?', const=True, default=False,
                               help="是否包含扣非净利润增长计算")
        args = arg_parser.parse_args()

        include_profit_growth = args.profit

        logger.info(f"执行参数: 包含利润增长计算={include_profit_growth}")

        # 执行股票概览分析
        success = generate_stock_overview(include_profit_growth)

        # 更新监控状态
        if success:
            quant_monitor(41, True)
            logger.info("✅ 股票概览分析系统执行完成")
        else:
            quant_monitor(41, False)
            logger.error("❌ 股票概览分析系统执行失败")

        return success

    except Exception as e:
        logger.error(f"股票概览分析系统执行失败: {e}")
        quant_monitor(41, False)
        return False

if __name__ == '__main__':

    setup_signal_handler()

    import sys
    success = main()
    cleanup_and_exit(0 if success else 1)