# -*- coding: UTF-8 -*-
"""
全股票量化分析任务（结构化优化版）
"""

import sys
import os
from pathlib import Path

# 自动注入项目根目录
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import threading
import json
import time
import argparse
import hashlib
import pandas as pd
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# 数据库与工具类
from db.database import DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode
from sqlalchemy.types import String, Float
from sqlalchemy import and_
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.logger import logger
from utils.helper import ago_day_timestr, sz_or_sh, check_bool
from db.fetch import fetch_all_stock, quant_monitor
from config.settings import get_settings

# Redis初始化
import redis
from tqdm import tqdm

def get_redis_client():
    settings = get_settings()
    return redis.Redis(
        host=getattr(settings, 'redis_host', os.getenv('BB_REDIS_HOST')),
        port=getattr(settings, 'redis_port', os.getenv('BB_REDIS_PORT')),
        db=7,
        password=getattr(settings, 'redis_password', os.getenv('BB_REDIS_PASSWORD'))
    )

akshare = Akshare()
bs = Baostock()

dtypedict = {
    'code': String(6),
    'date': String(10),
    'tradedate': String(8),
    'open': Float,
    'high': Float,
    'low': Float,
    'close': Float,
    'volume': Float,
    'amount': Float,
    'turn': Float,
    'pctChg': Float,
    'peTTM': Float,
    'pbMRQ': Float,
    'max': String(1),
    'min': String(1),
    'rose3': String(1),
    'fall3': String(1),
    'ret30': Float,
}

# 是否是30天最高价
def isMaxClosePrice(df, row):
    if row['rowIndex'] < 29 or len(df) <= 30:
        return '0'
    rows_before_current = df[df.rowIndex <= row['rowIndex']]
    df_30 = rows_before_current.iloc[-30:]
    max_30 = df_30['close'].max()
    if row['close'] == max_30:
        return '1'
    return '0'

# 是否是30天最低价
def isMinClosePrice(df, row):
    if row['rowIndex'] < 29 or len(df) <= 30:
        return '0'
    rows_before_current = df[df.rowIndex <= row['rowIndex']]
    df_30 = rows_before_current.iloc[-30:]
    min_30 = df_30['close'].min()
    if row['close'] == min_30:
        return '1'
    return '0'

# 是否三天上涨
def isThreeDayRose(df, row):
    if row['rowIndex'] < 2 or len(df) < 3:
        return '0'
    rows_before_current = df[df.rowIndex <= row['rowIndex']]
    df_3 = rows_before_current.iloc[-3:]
    if df_3.iloc[0]['pctChg'] > 0 and df_3.iloc[1]['pctChg'] > 0 and df_3.iloc[2]['pctChg'] > 0:
        return '1'
    return '0'

# 是否三天下跌
def isThreeDayFall(df, row):
    if row['rowIndex'] < 2 or len(df) < 3:
        return '0'
    rows_before_current = df[df.rowIndex <= row['rowIndex']]
    df_3 = rows_before_current.iloc[-3:]
    if df_3.iloc[0]['pctChg'] < 0 and df_3.iloc[1]['pctChg'] < 0 and df_3.iloc[2]['pctChg'] < 0:
        return '1'
    return '0'

def rowIndex(row):
    return row.name

# 任务队列名称
task_queue = 'stock_data_queue'

# 定义两个获取股票数据的方法
def method1(task):
    logger.info(f"Method1 processing: {task}")
    return akshare.query_history_data(task['stock_code'], task['start_date'], task['end_date'])

def method2(task):
    logger.info(f"Method2 processing: {task}")
    return bs.query_history_data(sz_or_sh(task['stock_code']), datetime.strptime(task['start_date'], '%Y%m%d').strftime('%Y-%m-%d'), datetime.strptime(task['end_date'], '%Y%m%d').strftime('%Y-%m-%d'))

# 工作进程函数
def worker(event, method, r):
    while not event.is_set():
        if event.is_set():
            logger.info("Event signaled. Exiting worker.")
            break
        if r.llen(task_queue) == 0:
            logger.info(f"{task_queue} is empty. Checking event to exit.")
            time.sleep(1)
            event.set()
            break
        try:
            _, task_json = r.brpop(task_queue)
            task = json.loads(task_json)
            begin = task['start_date']
            end = task['end_date']
            onlyend = task['onlyend']
            stock = task['stock_code']
            failed = task['fail_count']
            if failed <= 3:
                df = method(task)
                if df is None or df.empty:
                    logger.warning(f"股票 {stock} 获取数据失败，重试次数: {failed + 1}")
                    task['fail_count'] = task['fail_count'] + 1
                    r.lpush(task_queue, json.dumps(task))
                    continue
                else:
                    logger.debug(f"股票 {stock} 获取数据成功，共 {len(df)} 条记录")
            else:
                logger.error(f"股票 {stock} 重试次数超过限制，跳过处理")
                continue
            #########   1.计算   ############
            df['rowIndex'] = df.apply(rowIndex, axis=1)
            df['id'] = df.apply(lambda x: hashlib.md5((str(x['code'])+str(x['tradedate'])).encode("utf-8")).hexdigest().lower().replace('-', ''), axis=1)
            df['max'] = df.apply(lambda x: isMaxClosePrice(df, x), axis=1)
            df['min'] = df.apply(lambda x: isMinClosePrice(df, x), axis=1)
            df['rose3'] = df.apply(lambda x: isThreeDayRose(df, x), axis=1)
            df['fall3'] = df.apply(lambda x: isThreeDayFall(df, x), axis=1)
            df = df.drop(columns=['rowIndex'])
            df = df.replace(r'^\s*$', 0, regex=True)
            #########   2.计算   ############
            if (onlyend):
                logger.debug(f"过滤前数据量: {len(df)}, 目标日期: {end}")
                df = df[df['tradedate']==end]
                logger.debug(f"过滤后数据量: {len(df)}")
                if df.empty:
                    logger.warning(f"股票 {stock} 在日期 {end} 没有数据，跳过处理")
                    continue
                session = DBSession()
                session.query(StockHistory).filter(and_(StockHistory.tradedate==end, StockHistory.code==stock)).delete()
                session.commit()

            # 检查数据库存入前的数据状态
            if df.empty:
                logger.error(f"股票 {stock} 数据库存入前 df 为空！")
                continue

            try:
                logger.info(f"准备存入数据库: 股票 {stock}, 数据量 {len(df)}")
                print(f"股票 {stock} 数据预览:")
                print(df.head())
                df.to_sql('stockhistory', con=engine, if_exists='append', index=False, dtype=dtypedict)
                logger.info(f"股票 {stock} 数据存入成功")
            except Exception as e:
                logger.error(f"股票 {stock} 数据存入失败: {e}")
                logger.error(f"DataFrame 信息: shape={df.shape}, columns={list(df.columns)}")
                if not df.empty:
                    logger.error(f"DataFrame 样本数据: {df.iloc[0].to_dict()}")
        except Exception as e:
            logger.error(f"Worker encountered error: {e}")
            continue

def sync_stock_dict():
    try:
        stock_info_a_code_name_df = akshare.stock_info_a_code_name()
        stock_info_a_code_name_df.to_sql('stockcode', con=engine, if_exists='replace', index=False, dtype={
            'code': String(6),
            'name': String(16)
        })
    except Exception as e:
        logger.error(e)
    exits_stocks = fetch_all_stock(mark=False)
    add_stocks = stock_info_a_code_name_df['code'].to_numpy()
    session = DBSession()
    for addcode in tqdm(list(set(add_stocks).difference(set(exits_stocks)))):
        new_stock = Stock(
            code=str(addcode), 
        )
        session.merge(new_stock)
        session.commit()
    session.close()

def run_job(begin, end, onlyend=False, r=None):
    stocks = fetch_all_stock(mark=False)[:100]
    r.delete(task_queue)
    for index, code in enumerate(tqdm(stocks)):
        task = {"stock_code": code, "start_date": begin, "end_date": end, 'onlyend': onlyend, 'fail_count': 0}
        r.lpush(task_queue, json.dumps(task))
    stop_event = threading.Event()
    thread1 = threading.Thread(target=worker, args=(stop_event, method1, r))
    thread2 = threading.Thread(target=worker, args=(stop_event, method2, r))
    thread1.start()
    thread2.start()
    thread1.join()
    thread2.join()

def main():
    arg_parser = argparse.ArgumentParser()
    arg_parser.add_argument("-d", "--date", required=False, type=str, help="输入指定日期 yyyymmdd")
    arg_parser.add_argument("-p", "--period", required=False, type=check_bool, nargs='?', const=True, default=False, help="爬取从开始到指定日期的所有数据")
    args = arg_parser.parse_args()
    end = args.date
    period = args.period
    begin = ago_day_timestr(125, '%Y%m%d')
    today = time.strftime('%Y%m%d', time.localtime(time.time()))
    r = get_redis_client()
    try:
        logger.info('同步所有股票code')
        sync_stock_dict()
    except Exception as e:
        logger.error(f'同步所有股票code失败: {e}')
    if end:
        if period:
            run_job(begin, end, r=r)
        else:
            run_job(begin, end, True, r=r)
    else:
        run_job(begin, today, True, r=r)
    quant_monitor(22, True)

if __name__ == '__main__':
    main()
