# -*- coding: UTF-8 -*-
"""
全股票量化分析任务（企业级优化版）

主要功能:
1. 多线程并发获取股票历史数据
2. 计算技术指标（30天最高/最低价、三天涨跌趋势）
3. 数据清洗和存储到数据库
4. 支持增量更新和全量更新模式

优化特性:
- 面向对象设计，提升代码可维护性
- 完善的错误处理和重试机制
- 详细的日志记录和性能监控
- 数据验证和质量保证
- 资源自动管理和清理
"""

import sys
import os
import threading
import json
import time
import argparse
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from contextlib import contextmanager

import pandas as pd
import redis
from tqdm import tqdm
from sqlalchemy.types import String, Float
from sqlalchemy import and_

# 自动注入项目根目录
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 数据库与工具类
from db.database import DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.logger import logger
from utils.helper import ago_day_timestr, sz_or_sh, check_bool
from db.fetch import fetch_all_stock, quant_monitor
from config.settings import get_settings

# 常量定义
TASK_QUEUE_NAME = 'stock_data_queue'
MAX_RETRY_COUNT = 3
DEFAULT_LOOKBACK_DAYS = 125
REDIS_DB_INDEX = 7

# 数据库字段类型映射
DATABASE_DTYPE_MAPPING = {
    'code': String(6),
    'date': String(10),
    'tradedate': String(8),
    'open': Float,
    'high': Float,
    'low': Float,
    'close': Float,
    'volume': Float,
    'amount': Float,
    'turn': Float,
    'pctChg': Float,
    'peTTM': Float,
    'pbMRQ': Float,
    'max': String(1),
    'min': String(1),
    'rose3': String(1),
    'fall3': String(1),
    'ret30': Float,
}


@dataclass
class TaskConfig:
    """任务配置数据类"""
    stock_code: str
    start_date: str
    end_date: str
    onlyend: bool = False
    fail_count: int = 0


class RedisManager:
    """Redis连接管理器"""

    def __init__(self):
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化Redis客户端"""
        try:
            settings = get_settings()
            self.client = redis.Redis(
                host=getattr(settings, 'redis_host', os.getenv('BB_REDIS_HOST')),
                port=getattr(settings, 'redis_port', os.getenv('BB_REDIS_PORT')),
                db=REDIS_DB_INDEX,
                password=getattr(settings, 'redis_password', os.getenv('BB_REDIS_PASSWORD'))
            )
            # 测试连接
            self.client.ping()
            logger.info("Redis连接初始化成功")
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {e}")
            raise

    @contextmanager
    def get_client(self):
        """获取Redis客户端的上下文管理器"""
        try:
            yield self.client
        except Exception as e:
            logger.error(f"Redis操作失败: {e}")
            raise

    def close(self):
        """关闭Redis连接"""
        if self.client:
            self.client.close()
            logger.info("Redis连接已关闭")

class TechnicalIndicatorCalculator:
    """技术指标计算器"""

    @staticmethod
    def add_row_index(df: pd.DataFrame) -> pd.DataFrame:
        """添加行索引"""
        df['rowIndex'] = df.index
        return df

    @staticmethod
    def generate_record_id(df: pd.DataFrame) -> pd.DataFrame:
        """生成记录唯一ID"""
        def _generate_id(row):
            raw_string = f"{row['code']}{row['tradedate']}"
            return hashlib.md5(raw_string.encode("utf-8")).hexdigest().lower().replace('-', '')

        df['id'] = df.apply(_generate_id, axis=1)
        return df

    @staticmethod
    def is_30day_max_price(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为30天最高价"""
        try:
            if row['rowIndex'] < 29 or len(df) <= 30:
                return '0'

            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_30 = rows_before_current.iloc[-30:]
            max_30 = df_30['close'].max()

            return '1' if row['close'] == max_30 else '0'
        except Exception as e:
            logger.warning(f"计算30天最高价失败: {e}")
            return '0'

    @staticmethod
    def is_30day_min_price(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为30天最低价"""
        try:
            if row['rowIndex'] < 29 or len(df) <= 30:
                return '0'

            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_30 = rows_before_current.iloc[-30:]
            min_30 = df_30['close'].min()

            return '1' if row['close'] == min_30 else '0'
        except Exception as e:
            logger.warning(f"计算30天最低价失败: {e}")
            return '0'

    @staticmethod
    def is_three_day_rise(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为三天连续上涨"""
        try:
            if row['rowIndex'] < 2 or len(df) < 3:
                return '0'

            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_3 = rows_before_current.iloc[-3:]

            all_positive = all(df_3['pctChg'] > 0)
            return '1' if all_positive else '0'
        except Exception as e:
            logger.warning(f"计算三天上涨失败: {e}")
            return '0'

    @staticmethod
    def is_three_day_fall(df: pd.DataFrame, row: pd.Series) -> str:
        """判断是否为三天连续下跌"""
        try:
            if row['rowIndex'] < 2 or len(df) < 3:
                return '0'

            rows_before_current = df[df['rowIndex'] <= row['rowIndex']]
            df_3 = rows_before_current.iloc[-3:]

            all_negative = all(df_3['pctChg'] < 0)
            return '1' if all_negative else '0'
        except Exception as e:
            logger.warning(f"计算三天下跌失败: {e}")
            return '0'

    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        try:
            logger.debug(f"开始计算技术指标，数据量: {len(df)}")

            # 添加行索引
            df = self.add_row_index(df)

            # 生成记录ID
            df = self.generate_record_id(df)

            # 计算技术指标
            df['max'] = df.apply(lambda x: self.is_30day_max_price(df, x), axis=1)
            df['min'] = df.apply(lambda x: self.is_30day_min_price(df, x), axis=1)
            df['rose3'] = df.apply(lambda x: self.is_three_day_rise(df, x), axis=1)
            df['fall3'] = df.apply(lambda x: self.is_three_day_fall(df, x), axis=1)

            # 清理临时列
            df = df.drop(columns=['rowIndex'])

            # 清理空字符串
            df = df.replace(r'^\s*$', 0, regex=True)

            logger.debug(f"技术指标计算完成，最终数据量: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            raise

class DataSourceManager:
    """数据源管理器"""

    def __init__(self):
        self.akshare = Akshare()
        self.baostock = Baostock()
        self.indicator_calculator = TechnicalIndicatorCalculator()

    def fetch_data_with_akshare(self, task_config: TaskConfig) -> pd.DataFrame:
        """使用 Akshare 获取数据（备用方法）"""
        try:
            logger.info(f"使用 Akshare 获取股票 {task_config.stock_code} 数据")
            return self.akshare.query_history_data(
                task_config.stock_code,
                task_config.start_date,
                task_config.end_date
            )
        except Exception as e:
            logger.warning(f"Akshare 获取股票 {task_config.stock_code} 数据失败: {e}")
            return pd.DataFrame()

    def fetch_data_with_baostock(self, task_config: TaskConfig) -> pd.DataFrame:
        """使用 Baostock 获取数据（主要方法）"""
        try:
            logger.info(f"使用 Baostock 获取股票 {task_config.stock_code} 数据")

            # 格式化日期
            start_date_formatted = datetime.strptime(task_config.start_date, '%Y%m%d').strftime('%Y-%m-%d')
            end_date_formatted = datetime.strptime(task_config.end_date, '%Y%m%d').strftime('%Y-%m-%d')

            return self.baostock.query_history_data(
                sz_or_sh(task_config.stock_code),
                start_date_formatted,
                end_date_formatted
            )
        except Exception as e:
            logger.warning(f"Baostock 获取股票 {task_config.stock_code} 数据失败: {e}")
            return pd.DataFrame()

    def process_stock_data(self, df: pd.DataFrame, task_config: TaskConfig) -> pd.DataFrame:
        """处理股票数据"""
        try:
            if df.empty:
                logger.warning(f"股票 {task_config.stock_code} 原始数据为空")
                return df

            logger.debug(f"开始处理股票 {task_config.stock_code} 数据，原始数据量: {len(df)}")

            # 计算技术指标
            df = self.indicator_calculator.calculate_all_indicators(df)

            # 处理 onlyend 过滤
            if task_config.onlyend:
                df = self._apply_onlyend_filter(df, task_config)

            logger.debug(f"股票 {task_config.stock_code} 数据处理完成，最终数据量: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"处理股票 {task_config.stock_code} 数据失败: {e}")
            return pd.DataFrame()

    def _apply_onlyend_filter(self, df: pd.DataFrame, task_config: TaskConfig) -> pd.DataFrame:
        """应用 onlyend 过滤逻辑"""
        try:
            logger.debug(f"应用 onlyend 过滤，目标日期: {task_config.end_date}")
            logger.debug(f"可用交易日期: {df['tradedate'].unique().tolist()}")

            df_filtered = df[df['tradedate'] == task_config.end_date]

            if df_filtered.empty:
                logger.warning(f"股票 {task_config.stock_code} 在目标日期 {task_config.end_date} 没有交易数据")

                # 使用最近的交易日数据
                if not df.empty:
                    latest_date = df['tradedate'].max()
                    logger.info(f"使用最近的交易日数据: {latest_date}")
                    df_filtered = df[df['tradedate'] == latest_date]
                else:
                    logger.warning(f"股票 {task_config.stock_code} 没有任何可用数据")

            return df_filtered

        except Exception as e:
            logger.error(f"应用 onlyend 过滤失败: {e}")
            return df


# 全局数据源管理器实例
data_source_manager = DataSourceManager()

# 工作进程函数
def worker(event, method, r):
    while not event.is_set():
        if event.is_set():
            logger.info("Event signaled. Exiting worker.")
            break
        if r.llen(task_queue) == 0:
            logger.info(f"{task_queue} is empty. Checking event to exit.")
            time.sleep(1)
            event.set()
            break
        try:
            _, task_json = r.brpop(task_queue)
            task = json.loads(task_json)
            begin = task['start_date']
            end = task['end_date']
            onlyend = task['onlyend']
            stock = task['stock_code']
            failed = task['fail_count']
            if failed <= 3:
                df = method(task)
                if df is None or df.empty:
                    task['fail_count'] = task['fail_count'] + 1
                    r.lpush(task_queue, json.dumps(task))
                    continue
            else:
                continue
            #########   1.计算   ############
            df['rowIndex'] = df.apply(rowIndex, axis=1)
            df['id'] = df.apply(lambda x: hashlib.md5((str(x['code'])+str(x['tradedate'])).encode("utf-8")).hexdigest().lower().replace('-', ''), axis=1)
            df['max'] = df.apply(lambda x: isMaxClosePrice(df, x), axis=1)
            df['min'] = df.apply(lambda x: isMinClosePrice(df, x), axis=1)
            df['rose3'] = df.apply(lambda x: isThreeDayRose(df, x), axis=1)
            df['fall3'] = df.apply(lambda x: isThreeDayFall(df, x), axis=1)
            df = df.drop(columns=['rowIndex'])
            df = df.replace(r'^\s*$', 0, regex=True)
            #########   2.计算   ############
            if (onlyend):
                df = df[df['tradedate']==end]
                session = DBSession()
                session.query(StockHistory).filter(and_(StockHistory.tradedate==end, StockHistory.code==stock)).delete()
                session.commit()
            try:
                print(df)
                df.to_sql('stockhistory', con=engine, if_exists='append', index=False, dtype=dtypedict)
            except Exception as e:
                logger.error(e)
        except Exception as e:
            logger.error(f"Worker encountered error: {e}")
            continue

def sync_stock_dict():
    try:
        stock_info_a_code_name_df = akshare.stock_info_a_code_name()
        stock_info_a_code_name_df.to_sql('stockcode', con=engine, if_exists='replace', index=False, dtype={
            'code': String(6),
            'name': String(16)
        })
    except Exception as e:
        logger.error(e)
    exits_stocks = fetch_all_stock(mark=False)
    add_stocks = stock_info_a_code_name_df['code'].to_numpy()
    session = DBSession()
    for addcode in tqdm(list(set(add_stocks).difference(set(exits_stocks)))):
        new_stock = Stock(
            code=str(addcode), 
        )
        session.merge(new_stock)
        session.commit()
    session.close()

def run_job(begin, end, onlyend=False, r=None):
    stocks = fetch_all_stock(mark=False)[:100]
    r.delete(task_queue)
    for index, code in enumerate(tqdm(stocks)):
        task = {"stock_code": code, "start_date": begin, "end_date": end, 'onlyend': onlyend, 'fail_count': 0}
        r.lpush(task_queue, json.dumps(task))
    stop_event = threading.Event()
    thread1 = threading.Thread(target=worker, args=(stop_event, method1, r))
    thread2 = threading.Thread(target=worker, args=(stop_event, method2, r))
    thread1.start()
    thread2.start()
    thread1.join()
    thread2.join()

def main():
    arg_parser = argparse.ArgumentParser()
    arg_parser.add_argument("-d", "--date", required=False, type=str, help="输入指定日期 yyyymmdd")
    arg_parser.add_argument("-p", "--period", required=False, type=check_bool, nargs='?', const=True, default=False, help="爬取从开始到指定日期的所有数据")
    args = arg_parser.parse_args()
    end = args.date
    period = args.period
    begin = ago_day_timestr(125, '%Y%m%d')
    today = time.strftime('%Y%m%d', time.localtime(time.time()))
    r = get_redis_client()
    try:
        logger.info('同步所有股票code')
        sync_stock_dict()
    except Exception as e:
        logger.error(f'同步所有股票code失败: {e}')
    if end:
        if period:
            run_job(begin, end, r=r)
        else:
            run_job(begin, end, True, r=r)
    else:
        run_job(begin, today, True, r=r)
    quant_monitor(22, True)

if __name__ == '__main__':
    main()
