# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, Float, Boolean, Date, Text, DateTime, create_engine, func
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

# 创建对象的基类:
Base = declarative_base()

class Tag(Base):
    __tablename__ = 'tag'

    id = Column(Integer, primary_key=True)
    name = Column(String(32))

class GroupStock(Base):
    __tablename__ = 'groupstock'

    id = Column(Integer, primary_key=True)
    code = Column(String(6))


class StockMin(Base):
    __tablename__ = 'stockmin'

    id = Column(String(32), primary_key=True)
    code = Column(String(6))
    date = Column(String(16))
    tradedate = Column(String(10))
    open = Column(Float)
    close = Column(Float)
    high = Column(Float)
    low = Column(Float)
    now = Column(Float)
    turnover = Column(Float)
    volume = Column(Float)
    pctchg = Column(Float)
    preclose = Column(Float)
    spike = Column(Boolean)

class Stockkline(Base):
    __tablename__ = 'stockkline'

    func = Column(String(20), primary_key=True)
    name = Column(String(48))
    desc = Column(String(360))
    type = Column(String(16))
    rate = Column(Float())

class News(Base):
    __tablename__ = 'news'

    newsID = Column(String(64), primary_key=True)
    time = Column(String(20))
    important = Column(Boolean)
    read = Column(Boolean)

class NewsStock(Base):
    __tablename__ = 'newsStock'

    id = Column(Integer, primary_key=True)
    code = Column(String(6))
    createdAt = Column(DateTime)

class MediaStock(Base):
    __tablename__ = 'mediastock'

    id = Column(Integer, primary_key=True)
    code = Column(String(6))
    mediaId = Column(Integer)
    createdAt = Column(DateTime)

class Aggregation(Base):
    __tablename__ = 'aggregation'

    id = Column(Integer, primary_key=True)
    createdAt = Column(DateTime)
    read = Column(Boolean)

class Media(Base):
    # 表的名字:
    __tablename__ = 'media'
    id = Column(Integer, primary_key=True)
    mediaId = Column(String(40))
    source = Column(String(8))
    title = Column(String(120))
    url = Column(String(200))
    content = Column(Text)
    createTime = Column(String(10))
    important = Column(Boolean)
    read = Column(Boolean)

class Report(Base):
    # 表的名字:
    __tablename__ = 'report'
    announcementId = Column(String(16), primary_key=True)
    announcementTime = Column(Float())
    secName = Column(String(16))
    secCode = Column(String(6))
    announcementTitle =Column(String(125))
    adjunctUrl = Column(String(125))
    announcementType = Column(String(64))    


class Research(Base):
    __tablename__ = 'research'

    id = Column(Float(), primary_key=True)
    publishTimeStm = Column(Float())
    important = Column(Boolean)
    publishdate = Column(String(10))

class Worth(Base):
    # 表的名字:
    __tablename__ = 'worth'
    id = Column(Integer, primary_key=True)
    date = Column(String(10))
    totalAmount =Column(Float)
    totalShare = Column(Float)
    worths = Column(Float)

class WorthInout(Base):
    # 表的名字:
    __tablename__ = 'worthInout'
    id = Column(Integer, primary_key=True)
    date = Column(String(10))
    money =Column(Float)
    type = Column(String(6))

class Amount(Base):
    # 表的名字:
    __tablename__ = 'amount'
    id = Column(Integer, primary_key=True)
    date = Column(String(8))
    firstLevelId = Column(Integer)
    secondLevelId = Column(Integer)
    strength = Column(Integer)
    sum = Column(Float)
    avg = Column(Float)
    count = Column(Integer)

class OpenMarket(Base):
    __tablename__ = 'openMarket'
    id = Column(String(32), primary_key=True)
    type = Column(String(8))
    timelimit = Column(String(4))
    interestrate = Column(String(8))
    amount = Column(String(8))
    date = Column(String(8))
    title = Column(String(32))


class StockCode(Base):
    # 表的名字:
    __tablename__ = 'stockcode'
    code = Column(String(6), primary_key=True)
    name = Column(String(16))

class StockQuant(Base):
    # 表的名字:
    __tablename__ = 'stockquant'
    code = Column(String(6), primary_key=True)
    date = Column(String(8))
    reason = Column(String(32))
    rate = Column(Float)


class StockHistory(Base):
    # 表的名字:
    __tablename__ = 'stockHistory'
    id = Column(Integer, primary_key=True)
    code = Column(String(6))
    date = Column(String(10))
    tradedate = Column(String(8))
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    amount = Column(Float)
    turn = Column(Float)
    pctChg = Column(Float)
    # peTTM = Column(Float)
    # psTTM = Column(Float)
    # pcfNcfTTM = Column(Float)
    # pbMRQ = Column(Float)
    # isST = Column(String(1))

class Csindex(Base):
    # 表的名字:
    __tablename__ = 'csindex'
    id = Column(Integer, primary_key=True)
    code = Column(String(6))
    date = Column(String(10))
    tradedate = Column(String(8))
    name = Column(String(8))
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    amount = Column(Float)
    pctChg = Column(Float)
    deviation = Column(Float)
    pe = Column(Float)
    type = Column(Integer)

class Daliy(Base):
    # 表的名字:
    __tablename__ = 'stockDaliy'

    # 表的结构:
    date = Column(String(8), primary_key=True)
    rzye = Column(Float)
    rzmre = Column(Float)
    rzche = Column(Float)
    rqye = Column(Float)
    rqmcl = Column(Float)
    rzrqye = Column(Float)
    rqyl = Column(Float)
    shClose = Column(Float)
    shOpen = Column(Float)
    shHigh = Column(Float)
    shLow = Column(Float)
    szClose = Column(Float)
    cybOpen = Column(Float)
    cybLow = Column(Float)
    cybHigh = Column(Float)
    cybClose = Column(Float)
    shClosePct = Column(Float)
    szClosePct = Column(Float)
    cybClosePct = Column(Float)
    volume = Column(Float)
    shVolume = Column(Float)
    szVolume = Column(Float)
    cybVolume = Column(Float)
    isOpen = Column(Boolean)
    SMB = Column(Float)
    HML = Column(Float)
    RMW = Column(Float)
    UMD = Column(Float)
    PMO = Column(Float)
    AR = Column(Float)
    UES = Column(Float)
    averagechange = Column(Float)
    averageprice = Column(Float)
    changeratio = Column(Float)
    limitdown= Column(Integer)
    limitup= Column(Integer)
    newhigh= Column(Integer)
    newlow= Column(Integer)
    sentiment = Column(Float)
    sentimentDesc = Column(Text)
    funds = Column(Float)
    fundsDesc = Column(Text)
    totalvalue = Column(Float)
    crowding = Column(Float)
    medianincrease = Column(Float)
    brokenpb = Column(Integer)
    rose3 = Column(Integer)
    fall3 = Column(Integer)
    starcount = Column(Integer)
    style = Column(String(8))

class Stock(Base):
    # 表的名字:
    __tablename__ = 'stock'

    # 表的结构:
    code = Column(String(6), primary_key=True)
    thisYearEps = Column(Float)
    lastYearActualEps = Column(Float)
    white = Column(Boolean)
    profit_time = Column(String(120))
    profit = Column(String(120))
    comments = Column(Integer)
    rate = Column(Float)
    limit = Column(Integer)
    isHeavyVolume = Column(Boolean)
    isTup = Column(Boolean)
    isBottomInversion = Column(Boolean)
    emv = Column(Boolean)
    momentum = Column(Boolean)
    quant = Column(Boolean)
    ma = Column(Boolean)
    decisionPercent = Column(Float)
    md = Column(Float)
    ret = Column(Float)
    ret10 = Column(Float)
    ret20 = Column(Float)
    ret100 = Column(Float)
    alpha = Column(Float)
    beta = Column(Float)
    yoypni = Column(Float)
    roe = Column(Float)
    peg = Column(Float)
    lastscore = Column(Float)
    score = Column(Float)
    score1 = Column(Float)
    score2 = Column(Float)
    score3 = Column(Float)
    score4 = Column(Float)
    desc1 = Column(String(120))
    desc2 = Column(String(120))
    desc3 = Column(String(120))
    macd = Column(Float)
    kline = Column(String(240))
    tags = Column(String(120))
    star = Column(Boolean)
    max30 = Column(Boolean)
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Source(Base):
    # 表的名字:
    __tablename__ = 'source'
    code = Column(String(6), primary_key=True)
    f1 = Column(Float)
    f2= Column(Float)
    f3 = Column(Float)
    f4 = Column(Float)
    f5 = Column(Float)
    f6 = Column(Float)
    f7 = Column(Float)
    f8 = Column(Float)
    f9 = Column(Float)
    f10 = Column(Float)
    f20 = Column(Float)

class QuantList(Base):
    __tablename__ = 'quant'
    name = Column(String(32), primary_key=True)
    desc = Column(String(120))
    list = Column(Text)

class Quant(Base):
    # 表的名字:
    __tablename__ = 'configuration'

    # 表的结构:
    id = Column(Integer, primary_key=True)
    name = Column(String(12))
    startDate = Column(String(16))
    endDate = Column(String(16))
    decisionDate = Column(String(16))
    averageAmount = Column(Float)
    averageValue = Column(Float)
    score1Weight = Column(Float)
    score2Weight = Column(Float)
    score3Weight = Column(Float)
    score4Weight = Column(Float)
    momentumDay = Column(Integer)
    momentumRet = Column(Float)
    stockMinPct = Column(Float)
    stockMinVolume = Column(Float)
    updateTime = Column(Date)
    stockMediaDay = Column(Integer)
    stockMediaTitle = Column(Integer)
    stockMediaContent = Column(Integer)
    stockMediaView = Column(Integer)
    summaryMediaDay = Column(Integer)
    allMediaDay = Column(Integer)
    researchDay = Column(Integer)
    researchRetainDay = Column(Integer)
    newsRetainDay = Column(Integer)
    mediaRetainDay = Column(Integer)
    stockMinRetainDay = Column(Integer)
    reportRetainDay = Column(Integer)
    aggregationRetainDay = Column(Integer)
    mediaReadDay = Column(Integer)
    newsReadDay = Column(Integer)

class Jieba(Base):
    __tablename__ = 'jieba'

    id = Column(Integer, primary_key=True)
    text = Column(String(16))
    ranking = Column(Integer)
    lastranking = Column(Integer)

class JiebaStop(Base):
    __tablename__ = 'jiebaStop'

    id = Column(Integer, primary_key=True)
    text = Column(String(16))


class Fund(Base):
    __tablename__ = 'fund'

    code = Column(String(16), primary_key=True)
    name = Column(String(48))
    star = Column(Boolean)
    worth = Column(Float)
    dayRate = Column(Float)
    yearRate = Column(Float)

class FundStock(Base):
    __tablename__ = 'fundStock'

    id = Column(Integer, primary_key=True)
    fundCode = Column(String(16))
    code = Column(String(6))
    amount = Column(Float)
    ratio = Column(Float)

class Task(Base):
    __tablename__ = 'task'

    id = Column(Integer, primary_key=True)
    success = Column(Float)
    fail = Column(Float)
    # lastUpdated = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    lastUpdated = Column(DateTime)
    updatedAt = Column(DateTime)