# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseDAO
from db.models import Stock, StockHistory, Source, Stockkline, StockCode
from utils.logger import logger

class StockCodeDAO(BaseDAO[StockCode]):
    """股票代码数据访问对象"""
    def __init__(self):
        super().__init__(StockCode)

    # 获取所有code，生成列表
    def get_all_codes(self) -> List[str]:
        try:
            with self.db_manager.get_session() as session:
                
                return [row.code for row in session.query(StockCode.code).all()]
        except SQLAlchemyError as e:
            logger.error(f"Error getting all codes: {e}")
            return []


class StockDAO(BaseDAO[Stock]):
    """股票数据访问对象"""
    
    def __init__(self):
        super().__init__(Stock)
    
    def get_all_active_stocks(self, mark: bool = False) -> List[Stock]:
        """获取所有活跃股票"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(Stock)
                if mark:
                    query = query.filter(Stock.star == True)
                return query.all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting active stocks: {e}")
            return []
    
    def get_stocks_by_tags(self, tags: List[str]) -> List[Stock]:
        """根据标签获取股票"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(Stock)
                for tag in tags:
                    query = query.filter(Stock.tags.like(f'%{tag}%'))
                return query.all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting stocks by tags {tags}: {e}")
            return []
    
    def get_top_stocks_by_decision_percent(self, limit: int = 100) -> List[Stock]:
        """根据决策百分比获取排名靠前的股票"""
        try:
            with self.db_manager.get_session() as session:
                return (session.query(Stock)
                       .filter(Stock.decisionPercent.isnot(None))
                       .order_by(desc(Stock.decisionPercent))
                       .limit(limit)
                       .all())
        except SQLAlchemyError as e:
            logger.error(f"Error getting top stocks by decision percent: {e}")
            return []
    
    def get_stocks_by_score_range(self, min_score: float, max_score: float) -> List[Stock]:
        """根据评分范围获取股票"""
        try:
            with self.db_manager.get_session() as session:
                return (session.query(Stock)
                       .filter(and_(Stock.score >= min_score, Stock.score <= max_score))
                       .order_by(desc(Stock.score))
                       .all())
        except SQLAlchemyError as e:
            logger.error(f"Error getting stocks by score range: {e}")
            return []
    
    def update_stock_quant_data(self, code: str, quant_data: Dict[str, Any]) -> bool:
        """更新股票量化数据"""
        try:
            with self.db_manager.get_session() as session:
                stock = session.query(Stock).filter(Stock.code == code).first()
                if stock:
                    for key, value in quant_data.items():
                        if hasattr(stock, key):
                            setattr(stock, key, value)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Error updating stock quant data for {code}: {e}")
            return False


class StockHistoryDAO(BaseDAO[StockHistory]):
    """股票历史数据访问对象"""
    
    def __init__(self):
        super().__init__(StockHistory)
    
    def get_history_by_code_and_date_range(self, code: str, start_date: str, end_date: str) -> List[StockHistory]:
        """根据股票代码和日期范围获取历史数据"""
        try:
            with self.db_manager.get_session() as session:
                return (session.query(StockHistory)
                       .filter(and_(
                           StockHistory.code == code,
                           StockHistory.tradedate >= start_date.replace('-', ''),
                           StockHistory.tradedate <= end_date.replace('-', '')
                       ))
                       .order_by(asc(StockHistory.tradedate))
                       .all())
        except SQLAlchemyError as e:
            logger.error(f"Error getting history for {code} from {start_date} to {end_date}: {e}")
            return []
    
    def get_latest_history_by_code(self, code: str, limit: int = 100) -> List[StockHistory]:
        """获取股票最新的历史数据"""
        try:
            with self.db_manager.get_session() as session:
                return (session.query(StockHistory)
                       .filter(StockHistory.code == code)
                       .order_by(desc(StockHistory.tradedate))
                       .limit(limit)
                       .all())
        except SQLAlchemyError as e:
            logger.error(f"Error getting latest history for {code}: {e}")
            return []
    
    def bulk_insert_history(self, history_data: List[Dict[str, Any]]) -> bool:
        """批量插入历史数据"""
        try:
            with self.db_manager.get_session() as session:
                # 使用批量插入优化性能
                session.bulk_insert_mappings(StockHistory, history_data)
                return True
        except SQLAlchemyError as e:
            logger.error(f"Error bulk inserting history data: {e}")
            return False


class SourceDAO(BaseDAO[Source]):
    """数据源访问对象"""
    
    def __init__(self):
        super().__init__(Source)
    
    def get_sources_by_value_threshold(self, threshold: float) -> List[Source]:
        """根据价值阈值获取数据源"""
        try:
            with self.db_manager.get_session() as session:
                return (session.query(Source)
                       .filter(Source.f20 >= threshold)
                       .all())
        except SQLAlchemyError as e:
            logger.error(f"Error getting sources by value threshold {threshold}: {e}")
            return []


class StockklineDAO(BaseDAO[Stockkline]):
    """K线数据访问对象"""
    
    def __init__(self):
        super().__init__(Stockkline)
    
    def get_all_kline_functions(self) -> List[str]:
        """获取所有K线函数"""
        try:
            with self.db_manager.get_session() as session:
                results = session.query(Stockkline.func).all()
                return [result.func for result in results if result.func]
        except SQLAlchemyError as e:
            logger.error(f"Error getting all kline functions: {e}")
            return []
