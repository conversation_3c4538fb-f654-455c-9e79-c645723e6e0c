#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据获取功能
用于验证 akshare 和 baostock 数据获取是否正常
"""

import sys
import os
from pathlib import Path

# 自动注入项目根目录
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import time
from datetime import datetime, timedelta
from utils.akshare import Akshare
from utils.baostock import Baostock
from utils.logger import logger
from utils.helper import sz_or_sh

def test_akshare():
    """测试 Akshare 数据获取"""
    print("=" * 50)
    print("测试 Akshare 数据获取")
    print("=" * 50)

    akshare = Akshare()

    # 测试多个股票代码和日期范围
    test_cases = [
        ("000001", 5),   # 平安银行，最近5天
        ("000001", 30),  # 平安银行，最近30天
        ("000002", 5),   # 万科A，最近5天
        ("600000", 5),   # 浦发银行，最近5天
    ]

    for test_stock, days_back in test_cases:
        print(f"\n--- 测试股票: {test_stock}, 回溯天数: {days_back} ---")

        # 测试日期
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y%m%d')

        print(f"开始日期: {start_date}")
        print(f"结束日期: {end_date}")

        try:
            # 先测试直接调用 akshare 库
            import akshare as ak
            print("直接调用 akshare 库:")
            formatted_start = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
            formatted_end = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"
            print(f"  格式化日期: {formatted_start} 至 {formatted_end}")

            raw_df = ak.stock_zh_a_hist(symbol=test_stock, period="daily",
                                       start_date=formatted_start, end_date=formatted_end, adjust="qfq")

            if raw_df is None:
                print("  ❌ 直接调用返回 None")
            elif raw_df.empty:
                print("  ❌ 直接调用返回空 DataFrame")
            else:
                print(f"  ✅ 直接调用成功，共 {len(raw_df)} 条记录")
                print(f"  原始列: {list(raw_df.columns)}")

            # 再测试我们的封装方法
            print("调用封装方法:")
            df = akshare.query_history_data(test_stock, start_date, end_date)

            if df is None:
                print("  ❌ 封装方法返回 None")
            elif df.empty:
                print("  ❌ 封装方法返回空 DataFrame")
            else:
                print(f"  ✅ 封装方法成功，共 {len(df)} 条记录")
                print("  数据列:", list(df.columns))
                print("  数据预览:")
                print(df.head(2))

                # 检查必要的列
                required_columns = ['code', 'date', 'tradedate', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg', 'turn']
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    print(f"  ⚠️  缺少列: {missing_columns}")
                else:
                    print("  ✅ 所有必要列都存在")

        except Exception as e:
            print(f"  ❌ 异常: {e}")
            import traceback
            traceback.print_exc()

def test_baostock():
    """测试 Baostock 数据获取"""
    print("\n" + "=" * 50)
    print("测试 Baostock 数据获取")
    print("=" * 50)
    
    bs = Baostock()
    
    # 测试股票代码
    test_stock = "000001"
    
    # 测试日期 (最近5天)
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
    
    print(f"测试股票: {test_stock}")
    print(f"开始日期: {start_date}")
    print(f"结束日期: {end_date}")
    
    try:
        df = bs.query_history_data(sz_or_sh(test_stock), start_date, end_date)
        
        if df is None:
            print("❌ 返回 None")
        elif df.empty:
            print("❌ 返回空 DataFrame")
        else:
            print(f"✅ 成功获取数据，共 {len(df)} 条记录")
            print("数据列:", list(df.columns))
            print("数据预览:")
            print(df.head())
            
            # 检查必要的列
            required_columns = ['code', 'date', 'tradedate', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg', 'turn']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"⚠️  缺少列: {missing_columns}")
            else:
                print("✅ 所有必要列都存在")
                
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()

def test_stock_list():
    """测试股票列表获取"""
    print("\n" + "=" * 50)
    print("测试股票列表获取")
    print("=" * 50)
    
    akshare = Akshare()
    
    try:
        df = akshare.stock_info_a_code_name()
        
        if df is None:
            print("❌ 返回 None")
        elif df.empty:
            print("❌ 返回空 DataFrame")
        else:
            print(f"✅ 成功获取股票列表，共 {len(df)} 只股票")
            print("数据列:", list(df.columns))
            print("数据预览:")
            print(df.head())
            
            # 检查必要的列
            required_columns = ['code', 'name']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"⚠️  缺少列: {missing_columns}")
            else:
                print("✅ 所有必要列都存在")
                
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始测试数据获取功能...")
    
    # 测试 Akshare
    test_akshare()
    
    # 测试 Baostock
    test_baostock()
    
    # 测试股票列表
    test_stock_list()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == '__main__':
    main()
