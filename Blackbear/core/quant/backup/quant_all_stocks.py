# -*- coding: UTF-8 -*-
"""
量化分析系统 - 全股票分析模块 (优化版本)

0 30 19 * * 1-5

按时间段大于60个交易日，收盘遍历个股

主要功能:
1. 底部反转（困境反转）- 持续下跌后的放量大阳，均线上穿
2. 连板计算 - 涨停连板数统计
3. 动量趋势Momentum - 过去30天收益率因子
4. 三连阳 - 连续三日上涨模式识别
5. 时间段涨跌幅 - 区间收益率计算
6. CAPM - 资本资产定价模型

执行步骤:
- run_step1: 个股量化指标计算
- run_step2: 标星和趋势个股筛选
- run_step3: 个股综合评分
- run_step4: 市场风格研判 (可选)
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import asyncio
import baostock as bs
import pandas as pd
import numpy as np
import time
import datetime
import re
import sys
from typing import List, Dict, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, Quant, Amount, Source, QuantList, StockQuant, Daliy

# 工具类
from utils.helper import isLimit
from utils.baostock import Baostock
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 数据获取
from db.fetch import (
    fetch_all_stock, fetch_quant, fetch_quant_raw, quant_monitor,
    fetch_all_kline, fetch_all_history, fetch_quant_stock, is_trade_date,
    fetch_all_stock_data, fetch_all_stock_source, fetch_all_stock_codes
)

# 技术分析
import talib
import statsmodels.api as sm
from sqlalchemy.dialects.mysql import insert
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

def cal_price_momentum(df, n=30):
    """
    计算近n天收益率

    :param df: 股票数据DataFrame
    :param n: 天数
    :return: 收益率
    """
    try:
        if len(df) < n + 1:
            return 0

        daylast = df.iloc[-1]
        dayN = df.iloc[-n-1]

        if dayN.close <= 0:
            return 0

        percent = round((daylast.close - dayN.close) / dayN.close, 4)
        return percent

    except Exception as e:
        logger.error(f"计算价格动量失败: {e}")
        return 0

# 全局变量声明
bs = None
session = None
stocks = None
klines = None
start_date = None
end_date = None
decision_date = None
average_amount = None
average_value = None
quantId = None
quantRaw = None
hs300 = None
sz50 = None
cy50 = None
zz500 = None
kc50 = None
ret_max = None
df_all = None
df_quant_stock = None
df_source = None
df_stock = None
stock_trend_dict = {}

@monitor_performance
def initialize_system():
    """
    初始化量化分析系统

    :return: 是否成功
    """
    global bs, session, stocks, klines, start_date, end_date, decision_date
    global average_amount, average_value, quantId, quantRaw, hs300, sz50, cy50
    global zz500, kc50, ret_max, df_all, df_quant_stock, df_source, df_stock

    try:
        logger.info("开始初始化量化分析系统...")

        # 初始化基础组件
        bs = Baostock()
        session = DBSession()

        # 获取基础配置
        stocks = fetch_all_stock(mark=False)
        klines = fetch_all_kline()
        start_date, end_date, decision_date, average_amount, average_value, quantId = fetch_quant()
        quantRaw = fetch_quant_raw()

        logger.info(f'统计时间：{start_date} - {end_date}, 决断时间点：{decision_date}')

        # 获取指数数据
        logger.info("加载指数数据...")

        def safe_get_index_data(index_code, index_name):
            """安全获取指数数据"""
            try:
                data = bs.query_history_data(index_code, start_date=start_date, end_date=end_date)
                if data is None or data.empty:
                    logger.warning(f"指数 {index_name} ({index_code}) 无历史数据")
                    return pd.DataFrame(columns=['date', 'close'])

                if 'date' not in data.columns or 'close' not in data.columns:
                    logger.warning(f"指数 {index_name} ({index_code}) 数据格式异常: {data.columns.tolist()}")
                    return pd.DataFrame(columns=['date', 'close'])

                result = data[['date','close']].set_index('date').sort_index().reset_index()
                logger.debug(f"成功加载指数 {index_name}: {len(result)} 条记录")
                return result

            except Exception as e:
                logger.error(f"获取指数 {index_name} ({index_code}) 数据失败: {e}")
                return pd.DataFrame(columns=['date', 'close'])

        # 沪深300
        hs300 = safe_get_index_data('sh.000300', '沪深300')

        # 上证50
        sz50 = safe_get_index_data('sh.000016', '上证50')

        # 创业板50
        cy50 = safe_get_index_data('sz.399673', '创业板50')

        # 中证500
        zz500 = safe_get_index_data('sh.000905', '中证500')

        # 科创50
        kc50 = safe_get_index_data('sh.000688', '科创50')

        # 计算动量基准
        index_momentums = [quantRaw.momentumRet]

        # 安全计算各指数动量
        index_data_list = [
            (hs300, '沪深300'),
            (sz50, '上证50'),
            (cy50, '创业板50'),
            (zz500, '中证500'),
            (kc50, '科创50')
        ]

        for index_data, index_name in index_data_list:
            try:
                if not index_data.empty and len(index_data) > quantRaw.momentumDay:
                    momentum = cal_price_momentum(index_data, quantRaw.momentumDay)
                    index_momentums.append(momentum)
                    logger.debug(f"{index_name} 动量: {momentum}")
                else:
                    logger.warning(f"{index_name} 数据不足，跳过动量计算")
            except Exception as e:
                logger.warning(f"计算 {index_name} 动量失败: {e}")

        ret_max = max(index_momentums) if index_momentums else 0

        logger.info(f"动量基准设定为: {ret_max}")

        # 获取股票数据
        logger.info("加载股票历史数据...")
        df_all = fetch_all_history(begin=start_date, end=end_date)
        df_quant_stock = fetch_quant_stock()
        df_source = fetch_all_stock_source()
        df_stock = fetch_all_stock_data()

        logger.info("✅ 系统初始化完成")
        return True

    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False

# talib k线识别
def cal_k(df):
    """
    TA-Lib K线模式识别

    :param df: 股票数据DataFrame
    :return: 识别到的K线模式，逗号分隔
    """
    try:
        if len(df) < 10:  # 需要足够的数据
            return ''

        o, hi, l, c = map((lambda x : np.array(df[x], dtype=float)),['open','high','low','close'])
        k = []

        for method in klines:
            try:
                if hasattr(talib, method):
                    r = getattr(talib, method)(o, hi, l, c)
                    if len(r) > 0 and r[-1] > 0:
                        k.append(method)
            except Exception:
                continue  # 跳过有问题的指标

        return ','.join(k)

    except Exception as e:
        logger.error(f"K线模式识别失败: {e}")
        return ''

def TEMV(data, fasttimeperiod, lasttimeperiod):
    """
    计算EMV技术指标

    :param data: 股票数据DataFrame
    :param fasttimeperiod: 快速周期
    :param lasttimeperiod: 慢速周期
    :return: EMV, MAEMV, SubEMV
    """
    try:
        temp = data.loc[:,['date','open','high','low','close','volume']].copy()
        temp['sub'] = 2

        emFront = talib.DIV(talib.ADD(temp['high'],temp['low']),temp['sub'])
        emFrontSub = talib.DIV(talib.ADD(temp['high'].shift(1),temp['low'].shift(1)),temp['sub'])
        emEnd = talib.DIV(talib.SUB(temp['high'],temp['low']),temp['volume'])
        em = talib.SUB(emFront, emFrontSub)*emEnd
        em = em.dropna()

        EMV = talib.SMA(em, fasttimeperiod)
        MAEMV = talib.SMA(EMV, lasttimeperiod)
        SubEMV = talib.SUB(EMV, MAEMV)

        return EMV, MAEMV, SubEMV

    except Exception as e:
        logger.error(f"TEMV计算失败: {e}")
        return pd.Series(), pd.Series(), pd.Series()

def cal_emv(df):
    """
    EMV指标计算

    :param df: 股票数据DataFrame
    :return: EMV信号
    """
    try:
        if len(df) < 20:
            return False

        emv, maemv, subemv = TEMV(df, 5, 10)

        if len(emv) == 0 or len(maemv) == 0:
            return False

        if pd.isna(emv.iloc[-1]) or pd.isna(maemv.iloc[-1]):
            return False

        if emv.iloc[-1] > 0 and maemv.iloc[-1] > 0 and emv.iloc[-1] > maemv.iloc[-1]:
            return True

        return False

    except Exception as e:
        logger.error(f"EMV计算失败: {e}")
        return False

def cal_limit_num(df):
    """
    计算涨停连板数

    :param df: 股票数据DataFrame
    :return: 连续涨停天数
    """
    try:
        if len(df) < 1:
            return 0

        df_reversed = df.iloc[::-1].reset_index()
        limit = 0

        # 统计连板数
        for stock in df_reversed.itertuples():
            if isLimit(stock):
                limit = limit + 1
            else:
                break

        return limit

    except Exception as e:
        logger.error(f"计算连板数失败: {e}")
        return 0

def cal_bottom_return(df):
    """
    时间段内最低点到目前的收益率

    :param df: 股票数据DataFrame
    :return: 从最低点到当前的收益率
    """
    try:
        if len(df) < 2:
            return 0

        daylast = df.iloc[-1]
        day1 = df.sort_values(by="close").iloc[0]

        if day1.close <= 0:
            return 0

        percent = round((daylast.close - day1.close) / day1.close, 4)
        return percent

    except Exception as e:
        logger.error(f"计算底部收益率失败: {e}")
        return 0

def cal_decision_date_return(df):
    """
    计算决断日收益率

    :param df: 股票数据DataFrame
    :return: 从开始到结束的收益率
    """
    try:
        if len(df) < 2:
            return 0

        daylast = df.iloc[-1]
        day1 = df.iloc[0]

        if day1.close <= 0:
            return 0

        percent = round((daylast.close - day1.close) / day1.close, 4)
        return percent

    except Exception as e:
        logger.error(f"计算决断日收益率失败: {e}")
        return 0

def cal_isTup(df):
    """
    计算是否三连阳

    :param df: 股票数据DataFrame
    :return: 是否为三连阳
    """
    try:
        if len(df) < 3:
            return False

        df_clean = df.dropna(axis=0)
        if len(df_clean) < 3:
            return False

        day1 = df_clean.iloc[-1]
        day2 = df_clean.iloc[-2]
        day3 = df_clean.iloc[-3]

        # 检查是否有pctChg字段，如果没有则计算
        if 'pctChg' not in df_clean.columns:
            # 简化版本：检查是否连续三天上涨
            return (day1.close > day1.open and
                   day2.close > day2.open and
                   day3.close > day3.open)

        # 原逻辑：涨幅在2%-5%之间的三连阳
        isTup = (day1.pctChg > 2 and day1.pctChg < 5 and
                day2.pctChg > 2 and day2.pctChg < 5 and
                day3.pctChg > 2 and day3.pctChg < 5)

        return isTup

    except Exception as e:
        logger.error(f"计算三连阳失败: {e}")
        return False

def cal_momentum(df):
    """
    计算动量信号

    :param df: 股票数据DataFrame
    :return: 是否有动量信号
    """
    try:
        momentum = False
        ret = cal_price_momentum(df, quantRaw.momentumDay)
        daylast = df.iloc[-1]

        # 检查是否有amount字段，如果没有使用volume
        if 'amount' in df.columns:
            volume10 = df['amount'].rolling(10).mean().iloc[-1]
        else:
            volume10 = df['volume'].rolling(10).mean().iloc[-1] * daylast.close  # 估算成交额

        # 检查是否有ma10字段
        ma10_value = daylast.ma10 if hasattr(daylast, 'ma10') else daylast.close

        if ret >= ret_max and daylast.close >= ma10_value and volume10 > 1 * 100000000:
            momentum = True

        return momentum

    except Exception as e:
        logger.error(f"计算动量信号失败: {e}")
        return False

def cal_isHeavyVolume(df):
    """
    放量实体阳

    :param df: 股票数据DataFrame
    :return: 是否为放量实体阳
    """
    try:
        if len(df) < 6:
            return False

        isHeavyVolume = False
        day1 = df.iloc[-1]
        day2t6 = df.iloc[-6:-2]

        # 统计近四天的数据比较，是否放量
        avg_volume = day2t6['volume'].mean()
        if avg_volume > 0 and day1.volume > avg_volume * 2:
            # 检查是否有pctChg字段
            if hasattr(day1, 'pctChg'):
                if day1.close > day1.open and day1.pctChg >= 3:
                    isHeavyVolume = True
            else:
                # 简化版本：涨幅超过3%
                pct_change = (day1.close - day1.open) / day1.open * 100
                if day1.close > day1.open and pct_change >= 3:
                    isHeavyVolume = True

        return isHeavyVolume

    except Exception as e:
        logger.error(f"计算放量实体阳失败: {e}")
        return False

def cal_bottom_inversion(df):
    """
    底部反转，量能反转

    :param df: 股票数据DataFrame
    :return: 是否为底部反转
    """
    try:
        if len(df) < 60:
            return False

        bottomInversion = False
        isHeavyVolume = cal_isHeavyVolume(df)
        daylast = df.iloc[-1]

        if isHeavyVolume and cal_price_momentum(df, 60) < -0.3:
            bottomInversion = True

        return bottomInversion

    except Exception as e:
        logger.error(f"计算底部反转失败: {e}")
        return False

def max_drawdown(df):
    """
    最大回撤

    :param df: 价格序列
    :return: 最大回撤
    """
    try:
        if len(df) < 2:
            return 0

        md = ((df.cummax() - df) / df.cummax()).max()
        return round(md, 4)

    except Exception as e:
        logger.error(f"计算最大回撤失败: {e}")
        return 0

def cal_high_max(df, days=30):
    """
    计算收盘价是否为最高价

    :param df: 股票数据DataFrame
    :param days: 天数
    :return: 是否为最高价
    """
    try:
        if len(df) < days:
            return False

        if days >= 30:
            df_30 = df.iloc[-days:-1]
            max_30 = df_30['high'].max()
            last = df.iloc[-1]
            if last.close >= max_30:
                return True

        return False

    except Exception as e:
        logger.error(f"计算最高价失败: {e}")
        return False

def cal_CAPM(df):
    """
    资本资产定价模型

    :param df: 股票数据DataFrame
    :return: (收益率, 最大回撤, alpha, beta)
    """
    try:
        if len(df) < 30:
            return 0, 0, 0, 0

        daylast = df.iloc[-1]
        dayfirst = df.iloc[0]

        if dayfirst.close <= 0:
            return 0, 0, 0, 0

        # 净值
        worth = round(daylast.close / dayfirst.close, 4)
        # 收益率
        ret = worth - 1
        # 最大回撤
        close = df['close']
        md = max_drawdown(close)

        # 长度不一样，不计算
        if len(df) != len(hs300):
            return ret, md, 0, 0

        stock = df[['date','close']].copy()
        stock = stock.set_index('date')
        stock = stock.sort_index(ascending=True)
        stock = stock.reset_index()
        Ri_stock = np.log(stock['close']/stock['close'].shift(1))
        Ri_stock = Ri_stock.dropna()

        # beta，alpha
        Rm = np.log(hs300['close'] / hs300['close'].shift(1))
        Rm = Rm.dropna()

        if len(Ri_stock) != len(Rm) or len(Ri_stock) < 10:
            return ret, md, 0, 0

        Rm_add = sm.add_constant(Rm)   # 增加常数列
        model = sm.OLS(endog=Ri_stock, exog=Rm_add)  #计算股票收益率关于沪深300的线性回归模型
        result = model.fit()   # 拟合

        # 年化
        alpha = round(result.params.iloc[0] * 250, 4)
        beta = round(result.params.iloc[1], 4)

        return ret, md, alpha, beta

    except Exception as e:
        logger.error(f"CAPM计算失败: {e}")
        return 0, 0, 0, 0

def cal_score(df):
    """
    计算综合评分

    :param df: 股票数据DataFrame
    :return: (score1, score2, score3, score4)
    """
    try:
        score1 = 60
        score2 = 60
        score3 = 60
        score4 = 60

        if len(df) < 10:
            return score1, score2, score3, score4

        # 5日，10日成交额
        if 'amount' in df.columns:
            volume10 = df['amount'].rolling(10).mean().iloc[-1]
            volume5 = df['amount'].rolling(5).mean().iloc[-1]
        else:
            # 如果没有amount字段，使用volume估算
            last_close = df.iloc[-1]['close']
            volume10 = df['volume'].rolling(10).mean().iloc[-1] * last_close
            volume5 = df['volume'].rolling(5).mean().iloc[-1] * last_close

        if volume10 > 1 * 100000000:
            score3 = score3 + 10

        # 近10日涨幅
        ret10 = cal_price_momentum(df, 10)

        if ret10 > 0.1:  # 10%
            score4 = score4 + 10

        return score1, score2, score3, score4

    except Exception as e:
        logger.error(f"计算评分失败: {e}")
        return 60, 60, 60, 60

def cal_macd(df):
    """
    简化的MACD信号计算

    :param df: 股票数据DataFrame
    :return: MACD信号 (1: 金叉, -1: 死叉, 0: 无信号)
    """
    try:
        if len(df) < 50:
            return 0

        close = df['close'].astype(float).values
        dif, dea, hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)

        # 去除NaN值
        valid_indices = ~(np.isnan(dif) | np.isnan(dea))
        if np.sum(valid_indices) < 2:
            return 0

        dif_clean = dif[valid_indices]
        dea_clean = dea[valid_indices]

        if len(dif_clean) < 2:
            return 0

        # 检查最近的金叉或死叉
        # 金叉：DIF上穿DEA
        if dif_clean[-1] > dea_clean[-1] and dif_clean[-2] <= dea_clean[-2]:
            return 1
        # 死叉：DIF下穿DEA
        elif dif_clean[-1] < dea_clean[-1] and dif_clean[-2] >= dea_clean[-2]:
            return -1

        return 0

    except Exception as e:
        logger.error(f"MACD计算失败: {e}")
        return 0

# 计算上穿均线
def cal_ma(df):
    """
    计算均线信号

    :param df: 股票数据DataFrame
    :return: 是否有均线多头排列信号
    """
    try:
        if len(df) < 30:
            return False

        daylast = df.iloc[-1]
        day2 = df.iloc[-2]

        # 检查是否有均线字段，如果没有则计算
        if not all(hasattr(daylast, attr) for attr in ['ma5', 'ma10', 'ma30']):
            # 计算均线
            close = df['close'].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma30 = close.rolling(30).mean()

            if len(ma5) < 2 or len(ma10) < 2 or len(ma30) < 2:
                return False

            # 多头排列：MA5 > MA10 > MA30 且前一天不是
            current_bullish = ma5.iloc[-1] >= ma10.iloc[-1] and ma5.iloc[-1] >= ma30.iloc[-1]
            previous_bearish = ma5.iloc[-2] < ma10.iloc[-2] and ma5.iloc[-2] < ma30.iloc[-2]

            return current_bullish and previous_bearish
        else:
            # 使用原有逻辑
            if (daylast.ma5 >= daylast.ma10 and daylast.ma5 >= daylast.ma30 and
                day2.ma5 < day2.ma10 and day2.ma5 < day2.ma30):
                return True

        return False

    except Exception as e:
        logger.error(f"计算均线信号失败: {e}")
        return False

# 计算量化录入收益
def cal_quant_rate(df, df_quant):
    """
    计算量化录入收益

    :param df: 股票数据DataFrame
    :param df_quant: 量化数据DataFrame
    """
    try:
        if df_quant.empty or df.empty:
            return

        for idex, q in df_quant.iterrows():
            percent = 0
            daylast = df.iloc[-1]

            # 查找交易日数据
            if 'tradedate' in df.columns:
                daytrade = df[df['tradedate'] == q.date]
            else:
                # 如果没有tradedate字段，使用date字段
                daytrade = df[df['date'] == q.date]

            if not daytrade.empty and daylast.close > 0:
                day1 = daytrade.iloc[0]
                if day1.close > 0:
                    percent = round((daylast.close - day1.close) / day1.close, 4)

                    with db_manager.get_session() as session:
                        new_stock = StockQuant(
                            code=q.code,
                            date=q.date,
                            rate=float(percent)
                        )
                        session.merge(new_stock)
                        session.commit()

    except Exception as e:
        logger.error(f"计算量化收益失败: {e}")

@monitor_performance
def quant_stock(code):
    """
    量化分析单只股票

    :param code: 股票代码
    :return: 分析结果字典
    """
    try:
        logger.debug(f"开始分析股票: {code}")

        # 读取单个数据效率太低，改为读取所有数据
        df = df_all[df_all['code'] == code].copy()
        df_quant = df_quant_stock[df_quant_stock['code'] == code].copy()

        if df.empty:
            logger.warning(f"股票 {code} 没有历史数据")
            return None

        df = df.sort_values('tradedate', ascending=True)

        # 计算均线
        ma_list = [5, 10, 30]
        for ma in ma_list:
            df['ma' + str(ma)] = df['close'].rolling(ma).mean()

        # reverse
        df = df.reset_index()
        df.dropna() 

        # 获取从决断时间点的数据
        df_interval = df[df['date'] >= decision_date]

        # 初始化默认值
        result = {
            'code': code,
            'limit': 0,
            'ret10': 0,
            'ret20': 0,
            'ret100': 0,
            'momentum': False,
            'isBottomInversion': False,
            'decisionPercent': 0,
            'isTup': False,
            'isHeavyVolume': False,
            'macd': 0,
            'kline': '',
            'ret': 0,
            'md': 0,
            'alpha': 0,
            'beta': 0,
            'emv': False,
            'score1': 0,
            'score2': 0,
            'score3': 0,
            'score4': 0,
            'ma': False,
            'max30': False
        }

        # 选取大于15个交易日的股票，排除新股
        if not df.empty and len(df) > 15 and not df_interval.empty:

            # 连板计算
            result['limit'] = cal_limit_num(df)

            # 10,20日收益率
            result['ret10'] = cal_price_momentum(df, 10)
            result['ret20'] = cal_price_momentum(df, 20)

            # 最低点到目前的收益率
            result['ret100'] = cal_bottom_return(df)

            # 动量
            result['momentum'] = cal_momentum(df)

            # 底部反转
            result['isBottomInversion'] = cal_bottom_inversion(df)

            # 决断日收益率
            result['decisionPercent'] = cal_decision_date_return(df_interval)

            # 是否三连阳
            result['isTup'] = cal_isTup(df)

            # 放量实体阳
            result['isHeavyVolume'] = cal_isHeavyVolume(df)

            # CAPM
            result['ret'], result['md'], result['alpha'], result['beta'] = cal_CAPM(df)

            # k线指标
            result['kline'] = cal_k(df)
            result['emv'] = cal_emv(df)

            # macd背离
            result['macd'] = cal_macd(df)

            # 上穿均线
            result['ma'] = cal_ma(df)

            result['max30'] = cal_high_max(df)

            # 评分
            result['score1'], result['score2'], result['score3'], result['score4'] = cal_score(df)

            # 计算量化收录日到目前的收益率
            cal_quant_rate(df, df_quant)

        # 存储到数据库
        with db_manager.get_session() as session:
            new_stock = Stock(
                code=code,
                limit=result['limit'],
                isBottomInversion=result['isBottomInversion'],
                isHeavyVolume=result['isHeavyVolume'],
                isTup=result['isTup'],
                ret=float(result['ret']),
                md=float(result['md']),
                alpha=float(result['alpha']),
                beta=float(result['beta']),
                momentum=result['momentum'],
                decisionPercent=float(result['decisionPercent']),
                ret10=float(result['ret10']),
                ret20=float(result['ret20']),
                ret100=float(result['ret100']),
                kline=result['kline'],
                emv=result['emv'],
                macd=result['macd'],
                ma=result['ma'],
                max30=result['max30'],
                score1=result['score1'],
                score2=result['score2'],
                score3=result['score3'],
                score4=result['score4']
            )
            session.merge(new_stock)
            session.commit()

        return [code, result['isBottomInversion'], result['isHeavyVolume'],
                result['isTup'], result['macd'], result['ma'], result['emv']]

    except Exception as e:
        logger.error(f"分析股票 {code} 失败: {e}")
        return None

quant_stock_list = []
# 回调
def quant_callback(request, result):
    # 只添加有效结果，过滤掉None值
    if result is not None:
        quant_stock_list.append(result)

def save_sql(name, desc, arr):
    """
    保存量化选股结果到数据库

    :param name: 策略名称
    :param desc: 策略描述
    :param arr: 股票代码列表
    """
    try:
        if not arr:
            logger.warning(f"策略 {name} 没有选出股票")
            return

        logger.info(f"保存策略 {name}: {len(arr)} 只股票")

        with db_manager.get_session() as session:
            # 保存到QuantList表
            item = {
                'name': name,
                'desc': desc,
                'list': (',').join(arr)
            }
            insert_stmt = insert(QuantList).values(**item)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
            session.execute(on_duplicate_key_stmt)

            today = time.strftime('%Y%m%d', time.localtime(time.time()))

            # 确保交易日录入
            if is_trade_date(today):
                # 批量插入StockQuant记录
                stock_quant_records = []
                for stock in arr:
                    stock_quant_records.append({
                        'code': stock,
                        'reason': name,
                        'date': today
                    })

                # 批量插入
                if stock_quant_records:
                    for record in stock_quant_records:
                        insert_stmt = insert(StockQuant).values(**record)
                        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**record)
                        session.execute(on_duplicate_key_stmt)

            session.commit()

    except Exception as e:
        logger.error(f"保存策略 {name} 失败: {e}")

@monitor_performance
def run_step1():
    """
    步骤1: 个股量化分析和策略筛选

    :return: 是否成功
    """
    try:
        logger.info("开始步骤1: 个股量化分析")

        # 清空结果列表
        global quant_stock_list
        quant_stock_list = []

        # 使用线程池进行并行分析
        executor = ThreadPoolExecutor(max_workers=10)
        future_to_stock = {
            executor.submit(quant_stock, stock): stock
            for stock in stocks
        }
        try:
            for future in as_completed(future_to_stock):
                stock = future_to_stock[future]
                try:
                    result = future.result()
                    if result is not None:
                        quant_stock_list.append(result)
                except Exception as e:
                    logger.error(f"处理股票 {stock} 异常: {e}")
        finally:
            for future in future_to_stock:
                if not future.done():
                    future.cancel()
            executor.shutdown(wait=False)

        logger.info(f"完成股票分析，共处理 {len(quant_stock_list)} 只股票")

        if not quant_stock_list:
            logger.warning("没有分析结果，跳过策略筛选")
            return False

        # 验证数据完整性
        logger.info(f"收集到 {len(quant_stock_list)} 个分析结果")

        # 验证数据结构
        valid_results = []
        for i, result in enumerate(quant_stock_list):
            if result is None:
                logger.warning(f"跳过第 {i} 个空结果")
                continue
            if not isinstance(result, list) or len(result) != 7:
                logger.warning(f"跳过第 {i} 个格式错误的结果: {type(result)}, 长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                continue
            valid_results.append(result)

        if not valid_results:
            logger.error("没有找到格式正确的分析结果")
            return False

        logger.info(f"验证通过，有效结果数: {len(valid_results)}")

        # 量化选股结果整理
        df_quant = pd.DataFrame(
            data=valid_results,
            columns=['code', 'isBottomInversion', 'isHeavyVolume', 'isTup', 'macd', 'ma', 'emv']
        )

        # 各种策略筛选和保存
        strategies = [
            ('[量化]底部反转', '持续下跌后的放量大阳，均线上穿', 'isBottomInversion', True),
            ('[量化]成交量是前5天的两倍', '放量实体阳线', 'isHeavyVolume', True),
            ('[量化]三连阳', '连续三日上涨模式', 'isTup', True),
            ('[量化]底背离', 'MACD底背离信号', 'macd', -1),
            ('[量化]5日线上穿10日线和30日线', '均线多头排列', 'ma', True),
            ('[量化]EMV信号', 'EMV技术指标信号', 'emv', True)
        ]

        for name, desc, column, value in strategies:
            try:
                if column in df_quant.columns:
                    selected_stocks = df_quant[df_quant[column] == value]['code'].tolist()
                    save_sql(name, desc, selected_stocks)
                else:
                    logger.warning(f"列 {column} 不存在，跳过策略 {name}")
            except Exception as e:
                logger.error(f"处理策略 {name} 失败: {e}")

        logger.info("步骤1完成: 个股量化分析和策略筛选")
        return True

    except Exception as e:
        logger.error(f"步骤1执行失败: {e}")
        return False

@monitor_performance
def run_step2():
    """
    步骤2: 标星个股计算和趋势股票筛选

    :return: 是否成功
    """
    try:
        logger.info("开始步骤2: 标星个股计算")

        today = time.strftime('%Y%m%d', time.localtime(time.time()))

        with db_manager.get_session() as session:
            # 清除之前的标星状态
            session.query(Stock).filter(Stock.quant == 1).update({
                "quant": False,
                "star": False
            })
            session.commit()

            # 构建查询SQL
            sql = '''
                SELECT a.code, a.decisionPercent, a.kline, a.momentum,
                       b.f20, b.f6, b.displayName
                FROM stock a
                LEFT JOIN source b ON b.code = a.code
                WHERE b.f20 > {value} AND b.f6 > {amount}
                  AND a.momentum = true
                ORDER BY a.decisionPercent DESC
                LIMIT 1000
            '''.format(value=quantRaw.averageValue, amount=quantRaw.averageAmount)

            # 执行查询
            result = session.execute(sql)
            rows = result.fetchall()

            if not rows:
                logger.warning("没有找到符合条件的标星股票")
                return False

            logger.info(f"找到 {len(rows)} 只符合标星条件的股票")

            # 更新每日统计
            new_daily = Daliy(
                date=today,
                starcount=len(rows)
            )
            session.merge(new_daily)

            # 批量更新标星状态
            star_codes = []
            for row in rows:
                star_codes.append(row['code'])

                item = {
                    'code': row['code'],
                    'star': True,
                    'quant': True
                }
                insert_stmt = insert(Stock).values(**item)
                on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
                session.execute(on_duplicate_key_stmt)

            session.commit()

            logger.info(f"步骤2完成: 标星 {len(star_codes)} 只股票")
            return True

    except Exception as e:
        logger.error(f"步骤2执行失败: {e}")
        return False

def quant_stock_score(stock):
    """
    计算股票综合评分

    :param stock: 股票对象
    """
    try:
        score1 = 0  # 基本面评分
        score2 = 0  # 技术面评分
        score3 = 0  # 资金面评分
        score4 = 0  # 风口面评分
        score = 0   # 综合评分

        # 获取股票源数据
        df_stock_source = df_source.loc[df_source['code'] == stock.code]
        if df_stock_source.empty:
            logger.warning(f"股票 {stock.code} 没有源数据")
            return

        source = df_stock_source.iloc[0]

        # score1: 基本面评分
        # 市值100亿以上 +10分
        # 如果纳入指数可 +20分
        # 如果季度业绩增幅超过30% +40分
        # 权重10%
        try:
            if hasattr(source, 'f20') and source.f20 > 10000000000:
                score1 += 10
        except (AttributeError, TypeError):
            pass

        # 指数成分股加分
        index_tags = ['中证A50', '中证A500', '上证50', '科创50', '创业板50',
                     '中证500', '中证1000', '沪深300', '中证2000']
        try:
            if hasattr(stock, 'tags') and stock.tags:
                stock_tags = stock.tags.split(',')
                for tag in stock_tags:
                    if tag in index_tags:
                        score1 += 20
                        break
        except (AttributeError, TypeError):
            pass

        # 业绩增长加分
        try:
            if hasattr(stock, 'profit') and stock.profit:
                profit_list = stock.profit.split(',')
                if (len(profit_list) > 1 and
                    profit_list[-2] and profit_list[-1] and
                    float(profit_list[-2]) != 0):
                    growth_rate = ((float(profit_list[-1]) - float(profit_list[-2])) /
                                 float(profit_list[-2]))
                    if growth_rate >= 0.3:
                        score1 += 40
        except (AttributeError, TypeError, ValueError, IndexError):
            pass

        score += min(100, score1) * quantRaw.score1Weight

        # score2: 技术面评分
        # 出现部分积极指标 +20，趋势个股 +30
        # 权重30%
        try:
            if hasattr(stock, 'limit') and stock.limit > 1:
                score2 += 10 * stock.limit
            if hasattr(stock, 'isTup') and stock.isTup:
                score2 += 20
            if hasattr(stock, 'isHeavyVolume') and stock.isHeavyVolume:
                score2 += 20
            if hasattr(stock, 'ma') and stock.ma:
                score2 += 20
            if hasattr(stock, 'momentum') and stock.momentum:
                score2 += 20
            if hasattr(stock, 'isBottomInversion') and stock.isBottomInversion:
                score2 += 20
            # 趋势个股
            if hasattr(stock, 'star') and stock.star:
                score2 += 30
        except (AttributeError, TypeError):
            pass

        score += min(100, score2) * quantRaw.score2Weight

        # score3: 资金面评分
        # 日常交易资金大于1亿 +30， 换手率 +20， 放量 +30
        # 权重40%
        try:
            if hasattr(source, 'f6') and source.f6 > 100000000:
                score3 += 30
            if hasattr(source, 'f8') and source.f8 > 10:
                score3 += 20
            if hasattr(stock, 'isHeavyVolume') and stock.isHeavyVolume:
                score3 += 30
        except (AttributeError, TypeError):
            pass

        score += min(100, score3) * quantRaw.score3Weight

        # score4: 风口面评分
        # 热度排名按分值计算
        # 权重20%
        try:
            if stock.code in stock_trend_dict:
                # 热度爬虫每小时爬一次，满分应该是2400分
                hot_score = stock_trend_dict[stock.code] / 10
                if hot_score > 0:
                    score4 += min(100, hot_score)

            # 风口用涨跌简化正负相关性
            if hasattr(source, 'f3') and source.f3 < -5:
                score4 = score4 / 2
        except (AttributeError, TypeError, KeyError):
            pass

        score += min(100, score4) * quantRaw.score4Weight

        # 存储评分结果到数据库
        with db_manager.get_session() as session:
            new_stock = Stock(
                code=stock.code,
                lastscore=getattr(stock, 'score', 0),
                score=score,
                score1=score1,
                score2=score2,
                score3=score3,
                score4=score4
            )
            session.merge(new_stock)
            session.commit()

        logger.debug(f"股票 {stock.code} 评分完成: {score:.2f}")

    except Exception as e:
        logger.error(f"计算股票 {stock.code} 评分失败: {e}")

@monitor_performance
def run_step3():
    """
    步骤3: 个股综合评分计算

    :return: 是否成功
    """
    try:
        logger.info("开始步骤3: 个股综合评分计算")

        # 数据预处理
        if df_stock.empty:
            logger.warning("没有股票数据，跳过评分计算")
            return False

        # 填充缺失值
        df_stock['score'].fillna(0, inplace=True)
        df_stock['star'].fillna(0, inplace=True)
        df_stock['limit'].fillna(0, inplace=True)
        df_stock['profit'].fillna('', inplace=True)

        # 获取股票趋势热度数据
        try:
            sql = 'SELECT SUM(score) as score, code FROM stocktrend GROUP BY code LIMIT 1000'
            with engine.connect() as conn:
                result = conn.execute(sql)
                for row in result:
                    stock_trend_dict[row['code']] = row['score']
            logger.info(f"加载股票热度数据: {len(stock_trend_dict)} 只股票")
        except Exception as e:
            logger.warning(f"加载股票热度数据失败: {e}")

        # 准备股票列表
        stocks = []
        for index, stock in df_stock.iterrows():
            stocks.append(stock)

        if not stocks:
            logger.warning("没有股票需要评分")
            return False

        logger.info(f"开始计算 {len(stocks)} 只股票的评分")

        # 使用线程池并行计算评分
        executor = ThreadPoolExecutor(max_workers=10)
        future_to_stock = {
            executor.submit(quant_stock_score, stock): stock
            for stock in stocks
        }
        try:
            for future in as_completed(future_to_stock):
                stock = future_to_stock[future]
                try:
                    future.result()  # 等待任务完成
                except Exception as e:
                    logger.error(f"计算股票 {stock.code} 评分异常: {e}")
        finally:
            for future in future_to_stock:
                if not future.done():
                    future.cancel()
            executor.shutdown(wait=False)

        logger.info("步骤3完成: 个股综合评分计算")
        return True

    except Exception as e:
        logger.error(f"步骤3执行失败: {e}")
        return False

# def run_step4():
#     stock_cons = [
#         { 'name': '大盘成长', 'symbol': '399372', 'type': 2},
#         { 'name': '大盘价值', 'symbol': '399373', 'type': 2},
#         { 'name': '中盘成长', 'symbol': '399374', 'type': 2},
#         { 'name': '中盘价值', 'symbol': '399375', 'type': 2},
#         { 'name': '小盘成长', 'symbol': '399376', 'type': 2},
#         { 'name': '小盘价值', 'symbol': '399377', 'type': 2},	
#     ]

#     today = time.strftime('%Y%m%d', time.localtime(time.time()))
#     max_style = ''
#     max_value = -20

#     for con in stock_cons:
#         sql = "SELECT AVG(b.f3) AS avg_per \
#             FROM stock AS a \
#             LEFT JOIN source AS b ON a.code = b.code \
#             WHERE a.tags like '%%{}%%'".format(con['name'])

#         with engine.connect() as conn:
#             result = conn.execute(sql).fetchall()[0]
#             if result:
#                 if result['avg_per'] > max_value:
#                     max_value = result['avg_per']
#                     max_style = con['name']

#                 max_value = max(max_value, result['avg_per'])

#     new_daliy = Daliy(
#         date=today,
#         style=max_style
#     )
#     session.merge(new_daliy)
#     session.commit()

@monitor_performance
def main():
    """
    主函数 - 执行完整的量化分析流程

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始量化分析系统")

        # 初始化系统
        if not initialize_system():
            logger.error("系统初始化失败，终止分析")
            return False

        # 执行步骤1: 个股量化分析
        step1_success = run_step1()
        if not step1_success:
            logger.error("步骤1失败，终止分析")
            return False

        # 执行步骤2: 标星个股计算
        step2_success = run_step2()
        if not step2_success:
            logger.warning("步骤2失败，但继续执行")

        # 执行步骤3: 综合评分计算
        step3_success = run_step3()
        if not step3_success:
            logger.warning("步骤3失败，但分析已完成")

        # 更新量化配置的更新时间
        try:
            with db_manager.get_session() as session:
                new_quant = Quant(
                    id=quantId,
                    updateTime=datetime.datetime.now()
                )
                session.merge(new_quant)
                session.commit()
        except Exception as e:
            logger.error(f"更新量化配置失败: {e}")

        # 登出baostock系统
        try:
            if bs:
                bs.logout()
                logger.info("baostock系统登出成功")
        except Exception as e:
            logger.warning(f"baostock登出失败: {e}")

        # 更新监控状态
        quant_monitor(27, True)

        logger.info("✅ 量化分析系统执行完成")
        return True

    except Exception as e:
        logger.error(f"量化分析系统执行失败: {e}")
        quant_monitor(27, False)
        return False

if __name__ == '__main__':

    setup_signal_handler()

    import sys
    success = main()
    cleanup_and_exit(0 if success else 1)
