# -*- coding: UTF-8 -*-
"""
多因子分析系统 (优化版本)

0 45 19 * * 1-5

主要功能:
1. Fama-French三因子模型计算
2. SMB (小市值因子) 和 HML (价值因子) 计算
3. AR (人气指标) 计算
4. 多因子风险模型构建

优化特性:
- 完善的错误处理和日志记录
- 性能监控和并发处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import datetime
import time
import argparse
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, Daliy, StockHistory

# 工具类
from utils.baostock import Baostock
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import sz_or_sh, ago_day_timestr, check_bool
from config.settings import get_settings

# 数据获取
from db.fetch import fetch_all_stock, quant_monitor
from sqlalchemy import and_

# 可视化库 (可选)
try:
    import seaborn as sns
    import matplotlib.pyplot as plt
    import matplotlib as mpl
    sns.set()
    mpl.rcParams['font.sans-serif'] = 'WenQuanYi Micro Hei'
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False
    logger.warning("可视化库不可用，跳过图表功能")

@dataclass
class FactorData:
    """因子数据类"""
    date: str
    smb: float  # 小市值因子
    hml: float  # 价值因子
    rmw: float = 0.0  # 盈利因子
    umd: float = 0.0  # 动量因子
    pmo: float = 0.0  # 情绪因子
    ar: float = 0.0   # 人气指标

@dataclass
class StockFactorData:
    """股票因子数据类"""
    code: str
    market_cap: float  # 市值
    book_to_market: float  # 账面市值比
    size_bucket: str  # 大小市值分组 (S/B)
    value_bucket: str  # 价值分组 (H/M/L)
    return_rate: float  # 收益率

class MultiFactorProcessor:
    """多因子分析处理器"""

    def __init__(self):
        self.settings = get_settings()
        self.bs = Baostock()
        self.table_name = 'stockhistory'

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        try:
            if hasattr(self.bs, 'logout'):
                self.bs.logout()
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")

    def get_trading_dates(self, begin: str, end: str) -> List[str]:
        """
        获取时间段内的交易日

        :param begin: 开始日期 (YYYYMMDD)
        :param end: 结束日期 (YYYYMMDD)
        :return: 交易日列表
        """
        try:
            logger.info(f'获取 {begin} 到 {end} 的交易日')

            with db_manager.get_session() as session:
                dailys = session.query(Daliy).filter(
                    and_(
                        Daliy.date >= begin,
                        Daliy.date <= end,
                        Daliy.isOpen == True
                    )
                ).order_by(Daliy.date.asc()).all()

                trading_dates = [d.date for d in dailys]

            logger.info(f'找到 {len(trading_dates)} 个交易日')
            return trading_dates

        except Exception as e:
            logger.error(f"获取交易日失败: {e}")
            return []

    @staticmethod
    def classify_by_quantile(value: float, df: pd.DataFrame, column: str) -> str:
        """
        按分位数分类

        :param value: 待分类的值
        :param df: 数据框
        :param column: 列名
        :return: 分类结果 (H/M/L)
        """
        try:
            if value >= df[column].quantile(0.7):
                return 'H'
            elif value < df[column].quantile(0.3):
                return 'L'
            else:
                return 'M'
        except Exception:
            return 'M'

    def calculate_ar_indicator(self, row: pd.Series) -> float:
        """
        计算AR人气指标

        :param row: 股票数据行
        :return: AR值
        """
        try:
            high = float(row['high'])
            open_price = float(row['open'])
            low = float(row['low'])

            if high == open_price or open_price == low:
                return 100.0

            ar = (high - open_price) / (open_price - low) * 100
            return ar

        except Exception as e:
            logger.debug(f"计算AR指标失败: {e}")
            return 100.0

    def calculate_average_ar(self, df: pd.DataFrame) -> float:
        """
        计算平均AR值

        :param df: 股票数据
        :return: 平均AR值
        """
        try:
            df['ar'] = df.apply(self.calculate_ar_indicator, axis=1)
            return round(df['ar'].mean(), 2)
        except Exception as e:
            logger.error(f"计算平均AR失败: {e}")
            return 100.0

    def calculate_smb_hml_factors(self, df: pd.DataFrame) -> Tuple[float, float]:
        """
        计算SMB和HML因子

        SMB = 小市值 - 大市值，市值因子
        HML = 高价值 - 低价值，价值因子

        :param df: 股票数据
        :return: (SMB, HML) 因子值
        """
        try:
            logger.debug("开始计算SMB和HML因子")

            # 数据预处理
            df = df.copy()
            df = df[df['turn'] != 0]  # 过滤换手率为0的数据
            df = df.dropna(subset=['volume', 'turn', 'pbMRQ', 'pctChg'])

            if df.empty:
                logger.warning("数据为空，返回默认因子值")
                return 0.0, 0.0

            # 计算流通市值
            df['mkt'] = df['volume'] / df['turn']

            # 按流通市值划分大小市值公司 (以中位数为界)
            df['SB'] = df['mkt'].map(lambda x: 'B' if x >= df['mkt'].median() else 'S')

            # 计算账面市值比：PB的倒数
            df['BM'] = 1 / df['pbMRQ'].replace(0, np.inf)
            df['BM'] = df['BM'].replace([np.inf, -np.inf], np.nan)
            df = df.dropna(subset=['BM'])

            if df.empty:
                logger.warning("账面市值比数据为空，返回默认因子值")
                return 0.0, 0.0

            # 划分高、中、低账面市值比公司
            df['HML_group'] = df['BM'].apply(
                lambda x: self.classify_by_quantile(x, df, 'BM')
            )

            # 组合分组标识
            df['SB_HML'] = df['SB'] + '/' + df['HML_group']

            # 计算收益率
            df['ret'] = df['pctChg'] / 100

            # 按组合计算加权收益率
            ret_groups = df.groupby(['SB_HML']).apply(
                lambda x: (x['ret'] * x['mkt']).sum() / x['mkt'].sum()
            ).reset_index().set_index('SB_HML')
            ret_groups.rename(columns={ret_groups.columns[-1]: 'ret'}, inplace=True)

            # 检查所需组合是否存在
            required_groups = ['S/L', 'S/M', 'S/H', 'B/L', 'B/M', 'B/H']
            missing_groups = [g for g in required_groups if g not in ret_groups.index]

            if missing_groups:
                logger.warning(f"缺少组合: {missing_groups}，使用可用数据计算")
                # 使用可用数据计算，缺失组合用0填充
                for group in missing_groups:
                    ret_groups.loc[group] = {'ret': 0.0}

            # 计算SMB因子: (SL + SM + SH)/3 - (BL + BM + BH)/3
            small_cap_ret = (
                ret_groups.at['S/L', 'ret'] +
                ret_groups.at['S/M', 'ret'] +
                ret_groups.at['S/H', 'ret']
            ) / 3

            big_cap_ret = (
                ret_groups.at['B/L', 'ret'] +
                ret_groups.at['B/M', 'ret'] +
                ret_groups.at['B/H', 'ret']
            ) / 3

            smb = round(small_cap_ret - big_cap_ret, 4)

            # 计算HML因子: (SH + BH)/2 - (SL + BL)/2
            high_bm_ret = (
                ret_groups.at['S/H', 'ret'] +
                ret_groups.at['B/H', 'ret']
            ) / 2

            low_bm_ret = (
                ret_groups.at['S/L', 'ret'] +
                ret_groups.at['B/L', 'ret']
            ) / 2

            hml = round(high_bm_ret - low_bm_ret, 4)

            logger.debug(f"SMB因子: {smb}, HML因子: {hml}")
            return smb, hml

        except Exception as e:
            logger.error(f"计算SMB和HML因子失败: {e}")
            return 0.0, 0.0

    def fetch_stock_data_by_code(self, code: str, begin: str, end: str) -> Optional[pd.DataFrame]:
        """
        根据股票代码获取历史数据

        :param code: 股票代码
        :param begin: 开始日期
        :param end: 结束日期
        :return: 股票数据DataFrame
        """
        try:
            sql = f'''
                SELECT * FROM {self.table_name}
                WHERE code = '{code}'
                AND tradedate >= '{begin}'
                AND tradedate <= '{end}'
                ORDER BY tradedate ASC
            '''

            df = pd.read_sql(sql, con=engine)
            return df if not df.empty else None

        except Exception as e:
            logger.error(f"获取股票 {code} 数据失败: {e}")
            return None

    def fetch_all_stock_history(self, date: str) -> Optional[pd.DataFrame]:
        """
        获取指定日期所有股票的历史数据

        :param date: 日期 (YYYYMMDD)
        :return: 所有股票数据DataFrame
        """
        try:
            sql = f'''
                SELECT * FROM {self.table_name}
                WHERE tradedate = '{date}'
                AND turn > 0
                AND volume > 0
                AND pbMRQ > 0
            '''

            df = pd.read_sql(sql, con=engine)

            if df.empty:
                logger.warning(f"日期 {date} 没有股票数据")
                return None

            # 数据清理
            df = df.dropna(subset=['volume', 'turn', 'pbMRQ', 'pctChg'])

            logger.debug(f"获取到 {len(df)} 只股票的数据")
            return df

        except Exception as e:
            logger.error(f"获取日期 {date} 股票数据失败: {e}")
            return None

    @monitor_performance
    def process_single_date_factors(self, date: str) -> bool:
        """
        处理单个日期的因子计算

        :param date: 日期 (YYYYMMDD)
        :return: 是否成功
        """
        try:
            logger.info(f"开始处理日期 {date} 的因子计算")

            # 获取股票数据
            df = self.fetch_all_stock_history(date)
            if df is None or df.empty:
                logger.warning(f"日期 {date} 数据为空，跳过处理")
                return False

            # 计算SMB和HML因子
            smb, hml = self.calculate_smb_hml_factors(df)

            # 计算AR指标
            ar = self.calculate_average_ar(df)

            # 保存因子数据
            success = self.save_factor_data(date, smb, hml, ar)

            if success:
                logger.info(f"✅ 日期 {date} 因子计算完成: SMB={smb}, HML={hml}, AR={ar}")
            else:
                logger.error(f"❌ 日期 {date} 因子数据保存失败")

            return success

        except Exception as e:
            logger.error(f"处理日期 {date} 因子计算失败: {e}")
            return False

    def save_factor_data(self, date: str, smb: float, hml: float, ar: float = 0.0) -> bool:
        """
        保存因子数据到数据库

        :param date: 日期
        :param smb: SMB因子
        :param hml: HML因子
        :param ar: AR指标
        :return: 是否成功
        """
        try:
            with db_manager.get_session() as session:
                new_daily = Daliy(
                    date=date,
                    SMB=float(smb),
                    HML=float(hml),
                    AR=float(ar)
                )
                session.merge(new_daily)
                session.commit()

            logger.debug(f"因子数据保存成功: 日期={date}")
            return True

        except Exception as e:
            logger.error(f"保存因子数据失败: {e}")
            return False

    @monitor_performance
    def calculate_multi_factors(self, begin: str, end: str, max_workers: int = 4) -> bool:
        """
        计算多因子数据

        :param begin: 开始日期
        :param end: 结束日期
        :param max_workers: 最大工作线程数
        :return: 是否成功
        """
        try:
            logger.info(f"开始计算多因子数据: {begin} - {end}")

            # 获取交易日
            trading_dates = self.get_trading_dates(begin, end)
            if not trading_dates:
                logger.error("没有找到交易日")
                return False

            logger.info(f"准备处理 {len(trading_dates)} 个交易日")

            # 使用线程池并发处理
            success_count = 0
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_date = {
                    executor.submit(self.process_single_date_factors, date): date
                    for date in trading_dates
                }

                # 处理结果
                for future in as_completed(future_to_date):
                    date = future_to_date[future]
                    try:
                        success = future.result()
                        if success:
                            success_count += 1
                    except Exception as e:
                        logger.error(f"处理日期 {date} 异常: {e}")

            logger.info(f"多因子计算完成: 成功 {success_count}/{len(trading_dates)} 个交易日")
            return success_count > 0

        except Exception as e:
            logger.error(f"计算多因子数据失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def cal_ff_main(begin: str, end: str) -> bool:
    """多因子计算 - 优化版本"""
    with MultiFactorProcessor() as processor:
        return processor.calculate_multi_factors(begin, end)

@monitor_performance
def main():
    """
    主函数 - 执行多因子分析系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始多因子分析系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="多因子分析系统 (优化版本)")
        arg_parser.add_argument("-d", "--date", required=False, type=str,
                               help="输入日期 YYYYMMDD")
        arg_parser.add_argument("-t", "--today", required=False, type=check_bool,
                               nargs='?', const=True, default=True,
                               help="是否只计算今天")
        arg_parser.add_argument("-p", "--period", required=False, type=check_bool,
                               nargs='?', const=True, default=False,
                               help="计算从开始到指定日期的所有数据")
        arg_parser.add_argument("-w", "--workers", required=False, type=int,
                               default=4, help="工作线程数")
        args = arg_parser.parse_args()

        date = args.date
        today = args.today
        period = args.period
        max_workers = args.workers

        # 设置时间范围
        begin = ago_day_timestr(125, '%Y%m%d')
        end = time.strftime('%Y%m%d', time.localtime())

        if date:
            if period:
                # 从默认开始日期到指定日期
                end = date
            else:
                # 只计算指定日期
                begin = date
                end = date
        elif today:
            # 只计算今天
            begin = end

        logger.info(f"执行参数: 开始日期={begin}, 结束日期={end}, 工作线程={max_workers}")

        # 执行多因子计算
        success = cal_ff_main(begin, end)

        # 更新监控状态
        if success:
            quant_monitor(27, True)
            logger.info("✅ 多因子分析系统执行完成")
        else:
            quant_monitor(27, False)
            logger.error("❌ 多因子分析系统执行失败")

        return success

    except Exception as e:
        logger.error(f"多因子分析系统执行失败: {e}")
        quant_monitor(27, False)
        return False

if __name__ == '__main__':

    setup_signal_handler()

    import sys
    success = main()
    cleanup_and_exit(0 if success else 1)
