# -*- coding: UTF-8 -*-
"""
每日量化数据计算系统 (优化版本)

0 15 19,20,22,23 * * 1-5

主要功能:
1. 每日量化数据计算 - 计算市场各项技术指标
2. 总市值爬取 - 获取沪深两市总市值数据
3. 市场风格分析 - 分析当日市场主导风格
4. 交易拥挤度计算 - 计算资金集中度指标

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import datetime
import time
import argparse
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode, Csindex
from sqlalchemy import and_

# 工具类
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.helper import isLimit, isDownLimit, ago_day_timestr, check_bool
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 数据获取
from db.fetch import fetch_all_stock, quant_monitor, fetch_all_history

# 第三方库
import akshare as ak
from sqlalchemy.dialects.mysql import insert
from concurrent.futures import ThreadPoolExecutor, as_completed

@dataclass
class DailyMetrics:
    """每日市场指标数据类"""
    date: str
    limitdown: int = 0
    limitup: int = 0
    newhigh: int = 0
    newlow: int = 0
    averagechange: float = 0.0
    averageprice: float = 0.0
    changeratio: float = 0.0
    crowding: float = 0.0
    medianincrease: float = 0.0
    brokenpb: int = 0
    rose3: int = 0
    fall3: int = 0
    UES: int = 0
    style: str = ''
    totalvalue: float = 0.0

class DailyQuantAnalyzer:
    """每日量化分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.akshare = Akshare()
        self.bs = Baostock()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        try:
            if hasattr(self.bs, 'logout'):
                self.bs.logout()
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")

    @staticmethod
    def cal_is_limit(row) -> str:
        """
        判断是否涨停

        :param row: 股票数据行
        :return: '1' 表示涨停，'0' 表示非涨停
        """
        try:
            return '1' if isLimit(row) else '0'
        except Exception as e:
            logger.error(f"判断涨停失败: {e}")
            return '0'

    @staticmethod
    def cal_is_down_limit(row) -> str:
        """
        判断是否跌停

        :param row: 股票数据行
        :return: '1' 表示跌停，'0' 表示非跌停
        """
        try:
            return '1' if isDownLimit(row) else '0'
        except Exception as e:
            logger.error(f"判断跌停失败: {e}")
            return '0'

    def cal_market_style(self, date: str) -> str:
        """
        计算当日市场主导风格

        :param date: 交易日期
        :return: 主导风格名称
        """
        try:
            with db_manager.get_session() as session:
                csindex = session.query(Csindex).filter(
                    and_(Csindex.tradedate == date, Csindex.type == 2)
                ).all()

                if not csindex:
                    logger.warning(f"日期 {date} 没有找到风格指数数据")
                    return ''

                max_style = ''
                max_value = -20

                for index in csindex:
                    if hasattr(index, 'pctChg') and index.pctChg > max_value:
                        max_style = index.name
                        max_value = index.pctChg

                logger.debug(f"日期 {date} 主导风格: {max_style} ({max_value}%)")
                return max_style

        except Exception as e:
            logger.error(f"计算市场风格失败: {e}")
            return ''

    def calculate_daily_metrics(self, df: pd.DataFrame, date: str) -> DailyMetrics:
        """
        计算单日市场指标

        :param df: 当日股票数据
        :param date: 交易日期
        :return: 每日指标对象
        """
        try:
            if df.empty:
                logger.warning(f"日期 {date} 没有股票数据")
                return DailyMetrics(date=date)

            logger.debug(f"计算日期 {date} 的市场指标，股票数量: {len(df)}")

            # 数据预处理
            df = df.copy()
            df['turn'] = df['turn'].replace(0, np.inf)
            df['mkt'] = df.apply(lambda x: x['volume'] / x['turn'] if x['turn'] != np.inf else 0, axis=1)
            df['uplimit'] = df.apply(lambda x: self.cal_is_limit(x), axis=1)
            df['downlimit'] = df.apply(lambda x: self.cal_is_down_limit(x), axis=1)

            # 基础分类
            df_up = df[df['pctChg'] > 0]
            df_down = df[df['pctChg'] < 0]
            df_max = df[df['max'] == '1'] if 'max' in df.columns else pd.DataFrame()
            df_min = df[df['min'] == '1'] if 'min' in df.columns else pd.DataFrame()
            df_uplimit = df[df['uplimit'] == '1']
            df_downlimit = df[df['downlimit'] == '1']

            # 连续涨跌统计
            df_rose3 = df[df['rose3'] == '1'] if 'rose3' in df.columns else pd.DataFrame()
            df_fall3 = df[df['fall3'] == '1'] if 'fall3' in df.columns else pd.DataFrame()

            # 交易拥挤度计算
            crowding = self.calculate_crowding(df)

            # 中位数涨幅
            median_increase = self.calculate_median_increase(df)

            # 破净股统计
            broken_pb = self.calculate_broken_pb(df)

            # 基础指标计算
            change_ratio = self.calculate_change_ratio(df_up, df_down)
            average_change = df['pctChg'].mean() if not df['pctChg'].empty else 0.0
            average_price = df['close'].mean() if not df['close'].empty else 0.0

            # 市场风格分析
            style = self.cal_market_style(date)

            # 构建结果对象
            metrics = DailyMetrics(
                date=date,
                limitdown=len(df_downlimit),
                limitup=len(df_uplimit),
                newhigh=len(df_max),
                newlow=len(df_min),
                averagechange=round(average_change, 4),
                averageprice=round(average_price, 2),
                changeratio=round(change_ratio, 4),
                crowding=crowding,
                medianincrease=median_increase,
                brokenpb=broken_pb,
                rose3=len(df_rose3),
                fall3=len(df_fall3),
                UES=len(df_max) + len(df_min),
                style=style
            )

            logger.info(f"日期 {date} 指标计算完成: 涨停{metrics.limitup}只, 跌停{metrics.limitdown}只, "
                       f"涨跌比{metrics.changeratio:.2f}, 拥挤度{metrics.crowding}%")

            return metrics

        except Exception as e:
            logger.error(f"计算日期 {date} 指标失败: {e}")
            return DailyMetrics(date=date)

    def calculate_crowding(self, df: pd.DataFrame) -> float:
        """
        计算交易拥挤度

        :param df: 股票数据
        :return: 拥挤度百分比
        """
        try:
            if df.empty or 'amount' not in df.columns:
                return 0.0

            df_amount_sort = df.sort_values(by=['amount'], ascending=[False])
            top_5_percent_count = max(1, round(len(df_amount_sort) * 0.05))
            df_5percent = df_amount_sort.iloc[:top_5_percent_count]

            total_amount = df_amount_sort['amount'].sum()
            if total_amount == 0:
                return 0.0

            crowding = round(df_5percent['amount'].sum() / total_amount, 4) * 100
            return crowding

        except Exception as e:
            logger.error(f"计算交易拥挤度失败: {e}")
            return 0.0

    def calculate_median_increase(self, df: pd.DataFrame) -> float:
        """
        计算中位数涨幅

        :param df: 股票数据
        :return: 中位数涨幅
        """
        try:
            if df.empty or 'pctChg' not in df.columns:
                return 0.0

            pct_changes = df['pctChg'].dropna().tolist()
            if not pct_changes:
                return 0.0

            median_increase = np.median(pct_changes)
            return round(median_increase, 4)

        except Exception as e:
            logger.error(f"计算中位数涨幅失败: {e}")
            return 0.0

    def calculate_broken_pb(self, df: pd.DataFrame) -> int:
        """
        计算破净股数量

        :param df: 股票数据
        :return: 破净股数量
        """
        try:
            if df.empty or 'pbMRQ' not in df.columns:
                return 0

            df_broken_pb = df[(df['pbMRQ'] > 0) & (df['pbMRQ'] < 1)]
            return len(df_broken_pb)

        except Exception as e:
            logger.error(f"计算破净股失败: {e}")
            return 0

    def calculate_change_ratio(self, df_up: pd.DataFrame, df_down: pd.DataFrame) -> float:
        """
        计算涨跌比

        :param df_up: 上涨股票数据
        :param df_down: 下跌股票数据
        :return: 涨跌比
        """
        try:
            up_count = len(df_up)
            down_count = len(df_down)

            if down_count == 0:
                return float('inf') if up_count > 0 else 0.0

            return up_count / down_count

        except Exception as e:
            logger.error(f"计算涨跌比失败: {e}")
            return 0.0

    @monitor_performance
    def cal_daily(self, begin: str, end: str) -> bool:
        """
        计算指定时间段的每日量化指标

        :param begin: 开始日期
        :param end: 结束日期
        :return: 是否成功
        """
        try:
            logger.info(f"开始计算每日量化指标: {begin} - {end}")

            # 获取历史数据
            df_all = fetch_all_history(begin=begin, end=end)
            if df_all.empty:
                logger.warning(f"时间段 {begin} - {end} 没有历史数据")
                return False

            # 获取交易日列表
            with db_manager.get_session() as session:
                trading_days = session.query(Daliy).filter(
                    and_(Daliy.date <= end, Daliy.date >= begin, Daliy.isOpen == True)
                ).order_by(Daliy.date.desc()).all()

            if not trading_days:
                logger.warning(f"时间段 {begin} - {end} 没有交易日数据")
                return False

            logger.info(f"找到 {len(trading_days)} 个交易日")

            # 逐日计算指标
            success_count = 0
            for daily in trading_days:
                try:
                    # 获取当日数据
                    df_day = df_all[df_all['tradedate'] == daily.date].copy()

                    if df_day.empty:
                        logger.warning(f"日期 {daily.date} 没有股票数据，跳过")
                        continue

                    # 计算指标
                    metrics = self.calculate_daily_metrics(df_day, daily.date)

                    # 保存到数据库
                    if self.save_daily_metrics(metrics):
                        success_count += 1

                except Exception as e:
                    logger.error(f"处理日期 {daily.date} 失败: {e}")
                    continue

            logger.info(f"每日量化指标计算完成: 成功处理 {success_count}/{len(trading_days)} 个交易日")
            return success_count > 0

        except Exception as e:
            logger.error(f"计算每日量化指标失败: {e}")
            return False

    def save_daily_metrics(self, metrics: DailyMetrics) -> bool:
        """
        保存每日指标到数据库

        :param metrics: 每日指标对象
        :return: 是否成功
        """
        try:
            with db_manager.get_session() as session:
                new_daily = Daliy(
                    date=metrics.date,
                    limitdown=metrics.limitdown,
                    limitup=metrics.limitup,
                    newhigh=metrics.newhigh,
                    newlow=metrics.newlow,
                    averagechange=metrics.averagechange,
                    averageprice=metrics.averageprice,
                    changeratio=metrics.changeratio,
                    crowding=metrics.crowding,
                    medianincrease=metrics.medianincrease,
                    brokenpb=metrics.brokenpb,
                    rose3=metrics.rose3,
                    fall3=metrics.fall3,
                    UES=metrics.UES,
                    style=metrics.style,
                    totalvalue=metrics.totalvalue
                )
                session.merge(new_daily)
                session.commit()

            logger.debug(f"保存日期 {metrics.date} 的指标数据成功")
            return True

        except Exception as e:
            logger.error(f"保存日期 {metrics.date} 的指标数据失败: {e}")
            return False

    def get_market_total_value(self, date: str) -> Optional[float]:
        """
        获取指定日期的市场总市值

        :param date: 交易日期 (格式: YYYYMMDD)
        :return: 总市值(万亿元)，失败返回None
        """
        try:
            logger.debug(f"获取日期 {date} 的市场总市值")

            # 深圳市场数据
            szse_total = self.get_szse_market_value(date)
            if szse_total is None:
                return None

            # 上海市场数据
            sse_total = self.get_sse_market_value(date)
            if sse_total is None:
                return None

            # 计算总市值 (万亿元)
            total_value = round(szse_total + sse_total, 2)

            logger.info(f"日期 {date} 总市值: {total_value}万亿元 (深圳: {szse_total}, 上海: {sse_total})")
            return total_value

        except Exception as e:
            logger.error(f"获取日期 {date} 总市值失败: {e}")
            return None

    def get_szse_market_value(self, date: str) -> Optional[float]:
        """
        获取深圳市场总市值

        :param date: 交易日期
        :return: 深圳市场总市值(万亿元)
        """
        try:
            stock_szse_summary_df = ak.stock_szse_summary(date=date)

            if stock_szse_summary_df.empty:
                logger.warning(f"日期 {date} 深圳市场数据为空")
                return None

            # 筛选主板A股数据
            main_board_df = stock_szse_summary_df[
                stock_szse_summary_df['证券类别'] == '主板A股'
            ].reset_index()

            if main_board_df.empty:
                logger.warning(f"日期 {date} 深圳主板A股数据为空")
                return None

            # 总市值转换为万亿元
            szse_value = main_board_df.loc[0]['总市值'] / 100000000
            return szse_value

        except Exception as e:
            logger.error(f"获取深圳市场总市值失败: {e}")
            return None

    def get_sse_market_value(self, date: str) -> Optional[float]:
        """
        获取上海市场总市值

        :param date: 交易日期
        :return: 上海市场总市值(万亿元)
        """
        try:
            stock_sse_deal_daily_df = ak.stock_sse_deal_daily(date=date)

            if stock_sse_deal_daily_df.empty:
                logger.warning(f"日期 {date} 上海市场数据为空")
                return None

            # 筛选市价总值数据
            market_value_df = stock_sse_deal_daily_df[
                stock_sse_deal_daily_df['单日情况'] == '市价总值'
            ].reset_index()

            if market_value_df.empty:
                logger.warning(f"日期 {date} 上海市价总值数据为空")
                return None

            # 获取主板A股市值
            sse_value = market_value_df.loc[0]['主板A']
            return sse_value

        except Exception as e:
            logger.error(f"获取上海市场总市值失败: {e}")
            return None

    @monitor_performance
    def calculate_total_value(self, begin: str, end: str) -> bool:
        """
        计算指定时间段的总市值

        :param begin: 开始日期
        :param end: 结束日期
        :return: 是否成功
        """
        try:
            logger.info(f"开始计算总市值: {begin} - {end}")

            # 获取交易日列表
            trading_dates = []
            with db_manager.get_session() as session:
                trading_days = session.query(Daliy).filter(
                    and_(Daliy.date <= end, Daliy.date >= begin, Daliy.isOpen == True)
                ).order_by(Daliy.date.desc()).all()

                # 在会话内提取日期数据，避免会话绑定问题
                trading_dates = [day.date for day in trading_days]

            if not trading_dates:
                logger.warning(f"时间段 {begin} - {end} 没有交易日数据")
                return False

            logger.info(f"找到 {len(trading_dates)} 个交易日需要计算总市值")

            # 逐日计算总市值
            success_count = 0
            for date in trading_dates:
                try:
                    total_value = self.get_market_total_value(date)

                    if total_value is not None:
                        # 更新数据库
                        if self.save_total_value(date, total_value):
                            success_count += 1

                except Exception as e:
                    logger.error(f"处理日期 {date} 总市值失败: {e}")
                    continue

            logger.info(f"总市值计算完成: 成功处理 {success_count}/{len(trading_dates)} 个交易日")
            return success_count > 0

        except Exception as e:
            logger.error(f"计算总市值失败: {e}")
            return False

    def save_total_value(self, date: str, total_value: float) -> bool:
        """
        保存总市值到数据库

        :param date: 交易日期
        :param total_value: 总市值
        :return: 是否成功
        """
        try:
            with db_manager.get_session() as session:
                new_daily = Daliy(
                    date=date,
                    totalvalue=float(total_value)
                )
                session.merge(new_daily)
                session.commit()

            logger.debug(f"保存日期 {date} 总市值 {total_value} 成功")
            return True

        except Exception as e:
            logger.error(f"保存日期 {date} 总市值失败: {e}")
            return False

# 总市值 (旧版本 - 已废弃，存在会话绑定问题)
def totalvalue_old(begin, end):
    daliys = session.query(Daliy).filter(and_(Daliy.date<=end, Daliy.date>=begin, Daliy.isOpen==True)).order_by(Daliy.date.desc()).all()

    for day in daliys:
    # if is_trade_date(today) == True:
        print(day.date)

        # 20150407之后数据格式变了，需要重新计算
        # 深圳
        stock_szse_summary_df = ak.stock_szse_summary(date=day.date)
        # 找到列名为"证券类别"，值为“主板A股”的行中的"总市值",并创建索引
        stock_szse_summary_df_0 = stock_szse_summary_df[stock_szse_summary_df['证券类别'] == '主板A股'].reset_index()

        # 上海
        stock_sse_deal_daily_df = ak.stock_sse_deal_daily(date=day.date)
        stock_sse_deal_daily_df_0 = stock_sse_deal_daily_df[stock_sse_deal_daily_df['单日情况'] == '市价总值'].reset_index()

        total = round(stock_szse_summary_df_0.loc[0]['总市值'] / 100000000 + stock_sse_deal_daily_df_0.loc[0]['主板A'], 2)

        new_daliy = Daliy(
            date=day.date, 
            totalvalue=float(total),
        )

        session.merge(new_daliy)
        session.commit()

    session.close()

# 重写原函数为优化版本
def cal_daliy(begin: str, end: str) -> bool:
    """每日量化指标计算 - 优化版本"""
    with DailyQuantAnalyzer() as analyzer:
        return analyzer.cal_daily(begin, end)

def totalvalue(begin: str, end: str) -> bool:
    """总市值计算 - 优化版本"""
    with DailyQuantAnalyzer() as analyzer:
        return analyzer.calculate_total_value(begin, end)

@monitor_performance
def main():
    """
    主函数 - 执行每日量化数据计算 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始每日量化数据计算系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="每日量化数据计算系统 (优化版本)")
        arg_parser.add_argument("-d", "--date", required=False, type=str,
                               help="输入指定日期 yyyymmdd")
        arg_parser.add_argument("-p", "--period", required=False, type=check_bool,
                               nargs='?', const=True, default=False,
                               help="爬取从开始到指定日期的所有数据")
        args = arg_parser.parse_args()

        end_date = args.date
        period = args.period

        # 设置时间范围
        begin_date = ago_day_timestr(125, '%Y%m%d')
        today = time.strftime('%Y%m%d', time.localtime(time.time()))

        # 确定执行范围
        if end_date:
            if period:
                start_date, target_date = begin_date, end_date
            else:
                start_date, target_date = end_date, end_date
        else:
            start_date, target_date = today, today

        logger.info(f"执行时间范围: {start_date} - {target_date}")

        # 执行计算 (直接使用优化版本)
        daily_success = cal_daliy(start_date, target_date)
        total_value_success = totalvalue(start_date, target_date)

        # 更新监控状态
        if daily_success and total_value_success:
            quant_monitor(38, True)
            logger.info("✅ 每日量化数据计算完成")
            return True
        else:
            quant_monitor(38, False)
            logger.error("❌ 每日量化数据计算失败")
            return False

    except Exception as e:
        logger.error(f"每日量化数据计算系统执行失败: {e}")
        quant_monitor(38, False)
        return False

if __name__ == '__main__':

    setup_signal_handler()

    import sys
    success = main()
    cleanup_and_exit(0 if success else 1)
    