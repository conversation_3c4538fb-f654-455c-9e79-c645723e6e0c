# -*- coding: utf-8 -*-
"""
AkShare数据源接口 - 优化版本

主要功能:
1. 股票历史数据查询
2. 实时行情数据获取
3. 指数数据查询
4. 基金数据查询
5. 财务指标数据
6. 数据清洗和格式化

优化特性:
- 完善的错误处理和重试机制
- 数据缓存和去重
- 请求频率控制
- 数据质量验证
- 统一的数据格式
"""

import time
import datetime
from typing import Optional, Dict, List, Any, Union
from functools import wraps
import pandas as pd
import akshare as ak
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from utils.logger import logger
from utils.performance import monitor_performance, cache_result
from utils.helper import safe_float, safe_int, validate_date_format


class RateLimiter:
    """请求频率限制器"""

    def __init__(self, max_requests: int = 100, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []

    def wait_if_needed(self):
        """如果需要，等待以满足频率限制"""
        now = time.time()

        # 清理过期的请求记录
        self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]

        # 检查是否超过限制
        if len(self.requests) >= self.max_requests:
            sleep_time = self.time_window - (now - self.requests[0])
            if sleep_time > 0:
                logger.info(f"请求频率限制，等待 {sleep_time:.2f} 秒")
                time.sleep(sleep_time)

        # 记录当前请求
        self.requests.append(now)


def rate_limit(limiter: RateLimiter):
    """频率限制装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            limiter.wait_if_needed()
            return func(*args, **kwargs)
        return wrapper
    return decorator


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """失败重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (2 ** attempt)  # 指数退避
                        logger.warning(f"第 {attempt + 1} 次尝试失败: {e}, {wait_time:.1f}秒后重试")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"所有重试失败: {e}")

            raise last_exception
        return wrapper
    return decorator


def convert_datetime_column(col_series: pd.Series) -> pd.Series:
    """转换datetime列为字符串格式"""
    try:
        if col_series.dtype == "datetime64[ns]":
            return col_series.dt.strftime('%Y-%m-%d')
        return col_series
    except Exception as e:
        logger.warning(f"转换日期列失败: {e}")
        return col_series


class AkshareDataSource:
    """AkShare数据源类 - 优化版本"""

    def __init__(self, max_requests_per_minute: int = 500):
        self.rate_limiter = RateLimiter(max_requests_per_minute, 60)

        # 配置requests会话
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        logger.info("AkShare数据源初始化完成")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close()

    @monitor_performance
    @retry_on_failure(max_retries=3)
    def get_stock_history(self, stock_code: str, start_date: str, end_date: str,
                         adjust: str = "qfq", include_indicators: bool = False) -> pd.DataFrame:
        """
        获取股票历史数据

        :param stock_code: 股票代码
        :param start_date: 开始日期 YYYY-MM-DD
        :param end_date: 结束日期 YYYY-MM-DD
        :param adjust: 复权类型 ("qfq", "hfq", "")
        :param include_indicators: 是否包含财务指标
        :return: 股票历史数据DataFrame
        """
        try:
            # 参数验证
            if not stock_code or not validate_date_format(start_date, fmt='%Y%m%d') or not validate_date_format(end_date, fmt='%Y%m%d'):
                raise ValueError("无效的股票代码或日期格式")

            self.rate_limiter.wait_if_needed()

            logger.debug(f"获取股票 {stock_code} 历史数据: {start_date} 至 {end_date}")

            # 将日期格式从 YYYYMMDD 转换为 YYYY-MM-DD (akshare需要的格式)
            formatted_start_date = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
            formatted_end_date = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

            # 获取基础行情数据
            logger.debug(f"调用 ak.stock_zh_a_hist: symbol={stock_code}, start_date={formatted_start_date}, end_date={formatted_end_date}")
            df = ak.stock_zh_a_hist(
                symbol=stock_code,
                period="daily",
                start_date=formatted_start_date,
                end_date=formatted_end_date,
                adjust=adjust
            )

            if df is None:
                logger.warning(f"股票 {stock_code} 返回 None")
                return pd.DataFrame()
            elif df.empty:
                logger.warning(f"股票 {stock_code} 返回空 DataFrame")
                return pd.DataFrame()
            else:
                logger.debug(f"股票 {stock_code} 原始数据: {len(df)} 条记录, 列: {list(df.columns)}")

            # 数据清洗和重命名
            df = self._clean_stock_data(df, stock_code)

            # 获取财务指标数据
            if include_indicators:
                try:
                    self.rate_limiter.wait_if_needed()
                    df_indicator = ak.stock_a_indicator_lg(symbol=stock_code)

                    if not df_indicator.empty:
                        df_indicator = self._clean_indicator_data(df_indicator, formatted_start_date, formatted_end_date)
                        df = df.merge(df_indicator, on='date', how='left')

                except Exception as e:
                    logger.warning(f"获取股票 {stock_code} 财务指标失败: {e}")

            # 最终数据处理
            df = self._finalize_stock_data(df)

            logger.debug(f"股票 {stock_code} 历史数据获取完成: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 历史数据失败: {e}")
            return pd.DataFrame()

    def _clean_stock_data(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """清洗股票基础数据"""
        try:
            # 删除不需要的列
            columns_to_drop = ['振幅', '涨跌额']
            df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

            # 重命名列
            column_mapping = {
                '股票代码': 'code',
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '涨跌幅': 'pctChg',
                '换手率': 'turn'
            }

            df = df.rename(columns=column_mapping)

            # 设置股票代码
            df['code'] = stock_code

            # 转换日期格式
            df['date'] = pd.to_datetime(df['date'])

            return df

        except Exception as e:
            logger.error(f"清洗股票基础数据失败: {e}")
            return df

    def _clean_indicator_data(self, df_indicator: pd.DataFrame,
                            start_date: str, end_date: str) -> pd.DataFrame:
        """清洗财务指标数据"""
        try:
            # 重命名列
            column_mapping = {
                'trade_date': 'date',
                'pe_ttm': 'peTTM',
                'pb': 'pbMRQ'
            }
            df_indicator = df_indicator.rename(columns=column_mapping)

            # 转换日期格式
            df_indicator['date'] = pd.to_datetime(df_indicator['date'])

            # 过滤日期范围
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            df_indicator = df_indicator[
                (df_indicator['date'] >= start_dt) &
                (df_indicator['date'] <= end_dt)
            ]

            # 删除不需要的列
            columns_to_drop = ['ps', 'pe', 'ps_ttm', 'dv_ratio', 'dv_ttm', 'total_mv']
            df_indicator = df_indicator.drop(
                columns=[col for col in columns_to_drop if col in df_indicator.columns]
            )

            return df_indicator

        except Exception as e:
            logger.error(f"清洗财务指标数据失败: {e}")
            return df_indicator

    def _finalize_stock_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """最终处理股票数据"""
        try:
            # 转换日期为字符串
            df = df.apply(convert_datetime_column, axis=0)

            # 生成交易日期字段
            df['tradedate'] = df['date'].str.replace('-', '')

            # 数值类型转换
            numeric_columns = ['pctChg', 'turn', 'volume', 'amount', 'open', 'high', 'low', 'close']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 数据验证
            df = df.dropna(subset=['date', 'code'])

            return df

        except Exception as e:
            logger.error(f"最终处理股票数据失败: {e}")
            return df

    @monitor_performance
    @retry_on_failure(max_retries=2)
    def get_stock_realtime(self, stock_codes: Union[str, List[str]]) -> pd.DataFrame:
        """
        获取股票实时行情

        :param stock_codes: 股票代码或代码列表
        :return: 实时行情DataFrame
        """
        try:
            self.rate_limiter.wait_if_needed()

            if isinstance(stock_codes, str):
                stock_codes = [stock_codes]

            logger.debug(f"获取 {len(stock_codes)} 只股票实时行情")

            # 获取实时行情
            df = ak.stock_zh_a_spot_em()

            if df.empty:
                return pd.DataFrame()

            # 过滤指定股票
            df = df[df['代码'].isin(stock_codes)]

            # 数据清洗
            df = self._clean_realtime_data(df)

            logger.debug(f"实时行情获取完成: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            return pd.DataFrame()

    def _clean_realtime_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗实时行情数据"""
        try:
            # 重命名列
            column_mapping = {
                '代码': 'code',
                '名称': 'name',
                '最新价': 'price',
                '涨跌幅': 'pct_chg',
                '涨跌额': 'change',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '最高': 'high',
                '最低': 'low',
                '今开': 'open',
                '昨收': 'pre_close'
            }

            df = df.rename(columns=column_mapping)

            # 数值类型转换
            numeric_columns = ['price', 'pct_chg', 'change', 'volume', 'amount',
                             'amplitude', 'high', 'low', 'open', 'pre_close']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            return df

        except Exception as e:
            logger.error(f"清洗实时行情数据失败: {e}")
            return df

    @monitor_performance
    @cache_result(ttl_seconds=3600)  # 缓存1小时
    def get_stock_list(self) -> pd.DataFrame:
        """
        获取股票列表

        :return: 股票列表DataFrame
        """
        try:
            self.rate_limiter.wait_if_needed()

            logger.debug("获取股票列表")

            # 获取A股列表
            df = ak.stock_info_a_code_name()

            if df.empty:
                return pd.DataFrame()

            # 数据清洗
            df = df.rename(columns={'code': 'stock_code', 'name': 'stock_name'})

            logger.debug(f"股票列表获取完成: {len(df)} 只股票")
            return df

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return pd.DataFrame()

    @monitor_performance
    @retry_on_failure(max_retries=2)
    def get_index_data(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取指数数据

        :param index_code: 指数代码
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 指数数据DataFrame
        """
        try:
            self.rate_limiter.wait_if_needed()

            logger.debug(f"获取指数 {index_code} 数据: {start_date} 至 {end_date}")

            # 获取指数数据
            df = ak.index_zh_a_hist(symbol=index_code, period="daily",
                                   start_date=start_date, end_date=end_date)

            if df.empty:
                return pd.DataFrame()

            # 数据清洗
            df = self._clean_index_data(df, index_code)

            logger.debug(f"指数 {index_code} 数据获取完成: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取指数 {index_code} 数据失败: {e}")
            return pd.DataFrame()

    def _clean_index_data(self, df: pd.DataFrame, index_code: str) -> pd.DataFrame:
        """清洗指数数据"""
        try:
            # 重命名列
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }

            df = df.rename(columns=column_mapping)
            df['code'] = index_code

            # 转换数据类型
            df['date'] = pd.to_datetime(df['date'])

            numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            return df

        except Exception as e:
            logger.error(f"清洗指数数据失败: {e}")
            return df


# 兼容性类名
class Akshare(AkshareDataSource):
    """兼容性类，保持原有接口"""

    def __init__(self):
        super().__init__()
        logger.info('akshare量化数据源初始化')

    def query_history_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """兼容性方法"""
        return self.get_stock_history(stock_code, start_date, end_date)

    def stock_info_a_code_name(self) -> pd.DataFrame:
        """兼容性方法 - 获取股票列表"""
        df = self.get_stock_list()
        if not df.empty:
            # 恢复原来的列名以保持兼容性
            df = df.rename(columns={'stock_code': 'code', 'stock_name': 'name'})
        return df

    def logout(self):
        """兼容性方法"""
        pass
