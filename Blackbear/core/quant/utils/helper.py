# -*- coding: utf-8 -*-

import argparse
import datetime
import time
from typing import Union, Optional, Any
from utils.logger import logger
import emoji
import re


def cal_price_momentum(df, n=30):
    """
    计算近n天收益率

    :param df: 股票数据DataFrame
    :param n: 天数
    :return: 收益率
    """
    try:
        if len(df) < n + 1:
            return 0

        daylast = df.iloc[-1]
        dayN = df.iloc[-n-1]

        if dayN.close <= 0:
            return 0

        percent = round((daylast.close - dayN.close) / dayN.close, 4)
        return percent

    except Exception as e:
        logger.error(f"计算价格动量失败: {e}")
        return 0


def check_bool(boolean: Union[bool, str]) -> bool:
    """
    将字符串或布尔值转换为布尔值

    :param boolean: 要转换的值
    :return: 布尔值
    :raises: argparse.ArgumentTypeError 当输入无法转换为布尔值时
    """
    if isinstance(boolean, bool):
        return boolean

    if isinstance(boolean, str):
        if boolean.lower() in ('yes', 'true', 't', 'y', '1'):
            return True
        elif boolean.lower() in ('no', 'false', 'f', 'n', '0'):
            return False

    raise argparse.ArgumentTypeError(f'Boolean value expected, got: {boolean}')


def ago_day_timestr(days: int, strf: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    获取指定天数前的时间字符串

    :param days: 天数
    :param strf: 时间格式
    :return: 格式化的时间字符串
    """
    try:
        day_ago = datetime.datetime.now() - datetime.timedelta(days=days)
        return day_ago.strftime(strf)
    except Exception as e:
        logger.error(f"Error formatting ago day time string: {e}")
        raise


def after_day_timestr(days: int, strf: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    获取指定天数后的时间字符串

    :param days: 天数
    :param strf: 时间格式
    :return: 格式化的时间字符串
    """
    try:
        day_after = datetime.datetime.now() + datetime.timedelta(days=days)
        return day_after.strftime(strf)
    except Exception as e:
        logger.error(f"Error formatting after day time string: {e}")
        raise


def ago_day_timestamp(days: int, ms: bool = True) -> int:
    """
    获取指定天数前的时间戳

    :param days: 天数
    :param ms: 是否返回毫秒时间戳
    :return: 时间戳
    """
    try:
        day_ago = datetime.datetime.now() - datetime.timedelta(days=days)

        if ms:
            return int(time.mktime(day_ago.timetuple()) * 1000.0 + day_ago.microsecond / 1000.0)
        else:
            return int(time.mktime(day_ago.timetuple()))
    except Exception as e:
        logger.error(f"Error generating ago day timestamp: {e}")
        raise


def today_timestr(strf: str = '%Y-%m-%d') -> str:
    """
    获取今天的时间字符串

    :param strf: 时间格式
    :return: 格式化的时间字符串
    """
    try:
        today = datetime.date.today()
        today_time = int(time.mktime(today.timetuple()))
        return time.strftime(strf, time.localtime(today_time))
    except Exception as e:
        logger.error(f"Error formatting today time string: {e}")
        raise


def today_timestamp(ms: bool = True) -> Union[int, float]:
    """
    获取今天的时间戳

    :param ms: 是否返回毫秒时间戳
    :return: 时间戳
    """
    try:
        today = datetime.date.today()
        today_time = int(time.mktime(today.timetuple()))

        if ms:
            return today_time * 1000.0
        else:
            return today_time
    except Exception as e:
        logger.error(f"Error generating today timestamp: {e}")
        raise


def is_not_number(num: str) -> bool:
    """
    检查字符串是否不是数字

    :param num: 要检查的字符串
    :return: 如果不是数字返回True，否则返回False
    """
    try:
        pattern = re.compile(r'^[-+]?[-0-9]\d*\.\d*|[-+]?\.?[0-9]\d*$')
        result = pattern.match(str(num))
        return result is None
    except Exception as e:
        logger.error(f"Error checking if string is number: {e}")
        return True


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    安全转换为浮点数

    :param value: 要转换的值
    :param default: 默认值
    :return: 浮点数
    """
    try:
        if value is None or value == '':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """
    安全转换为整数

    :param value: 要转换的值
    :param default: 默认值
    :return: 整数
    """
    try:
        if value is None or value == '':
            return default
        return int(float(value))
    except (ValueError, TypeError):
        return default


def clear_html(src_html: str = '') -> str:
    """
    清理HTML标签和特殊字符

    :param src_html: 源HTML字符串
    :return: 清理后的字符串
    """
    try:
        if not src_html:
            return ''

        # 去除HTML标签
        content = re.sub(r"</?(.+?)>", "", str(src_html))
        # 去除多余空白字符
        dst_html = re.sub(r"\s+", " ", content).strip()
        # 替换HTML实体
        dst_html = dst_html.replace('&nbsp;', ' ')
        dst_html = dst_html.replace('&amp;', '&')
        dst_html = dst_html.replace('&lt;', '<')
        dst_html = dst_html.replace('&gt;', '>')

        # 去除emoji
        return emoji.demojize(dst_html)
    except Exception as e:
        logger.error(f"Error clearing HTML: {e}")
        return str(src_html)


def get_limit_percentage(code: str, trade_date: str) -> float:
    """
    根据股票代码和交易日期获取涨跌停比例

    :param code: 股票代码
    :param trade_date: 交易日期
    :return: 涨跌停比例
    """
    try:
        # 科创板
        if code.startswith('sh.688'):
            return 0.2

        # 创业板注册制
        if (code.startswith('sz.300') and
            datetime.datetime.strptime(trade_date, "%Y-%m-%d") >=
            datetime.datetime.strptime('2020-08-24', "%Y-%m-%d")):
            return 0.2

        # 默认10%
        return 0.1
    except Exception as e:
        logger.error(f"Error getting limit percentage for {code}: {e}")
        return 0.1


def isLimit(d) -> bool:
    """
    判断是否涨停

    :param d: 包含股票数据的对象，需要有close, pctChg, code, date属性
    :return: 是否涨停
    """
    try:
        if not hasattr(d, 'close') or not hasattr(d, 'pctChg') or not hasattr(d, 'code'):
            return False

        if d.close <= 0 or d.close is None or d.pctChg is None:
            return False

        per = get_limit_percentage(d.code, d.date)

        # 根据涨跌幅pctChg和今日收盘价close倒推出昨日收盘价preclose
        if d.pctChg == -100:  # 避免除零错误
            return False

        preclose = d.close / (d.pctChg / 100 + 1)
        close_cal = round(float(preclose) * (1 + per), 2)

        return abs(float(close_cal) - float(d.close)) < 0.01  # 使用小的误差范围
    except Exception as e:
        logger.error(f"Error checking limit up for {getattr(d, 'code', 'unknown')}: {e}")
        return False


def isDownLimit(d) -> bool:
    """
    判断是否跌停

    :param d: 包含股票数据的对象，需要有close, pctChg, code, date属性
    :return: 是否跌停
    """
    try:
        if not hasattr(d, 'close') or not hasattr(d, 'pctChg') or not hasattr(d, 'code'):
            return False

        if d.close <= 0 or d.close is None or d.pctChg is None:
            return False

        per = get_limit_percentage(d.code, d.date)

        if d.pctChg == -100:  # 避免除零错误
            return True

        preclose = d.close / (d.pctChg / 100 + 1)
        close_cal = round(float(preclose) * (1 - per), 2)

        return abs(float(close_cal) - float(d.close)) < 0.01  # 使用小的误差范围
    except Exception as e:
        logger.error(f"Error checking limit down for {getattr(d, 'code', 'unknown')}: {e}")
        return False


def sz_or_sh(code: str) -> Optional[str]:
    """
    根据股票代码判断是上海还是深圳交易所，并添加前缀

    :param code: 6位股票代码
    :return: 带前缀的股票代码，如果输入无效则返回None
    """
    try:
        if not code or len(code) != 6 or not code.isdigit():
            return None

        if code.startswith('6'):
            return f'sh.{code}'
        else:
            return f'sz.{code}'
    except Exception as e:
        logger.error(f"Error processing stock code {code}: {e}")
        return None


def validate_date_format(date_str: str, fmt: str = '%Y-%m-%d') -> bool:
    """
    验证日期格式是否正确

    :param date_str: 日期字符串
    :param fmt: 日期格式
    :return: 格式是否正确
    """
    try:
        datetime.datetime.strptime(date_str, fmt)
        return True
    except ValueError:
        return False
    