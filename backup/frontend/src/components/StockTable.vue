<template>
    <div>
        <a-table 
            v-if="source"
            :bordered="bordered"
            :loading="loading"
            :pagination="pn"
            :scroll="{ x: width, y: height }"
            :customRow="customRow"
            :columns="getColumns"
            :rowKey="record => record.f12"
            :dataSource="dataSource"
            @change="handleTableChange">
            <template slot="displayName" slot-scope="text, record">
                <!-- <span :class="{'line': record.black}"> -->
                    <!-- <StockSign :code="record.code" />{{record.source.displayName}} -->
                    <!-- <span class="fs12">{{record.source.code}}</span> -->
                <!-- </span> -->
                <StockBar :stock="record.stock" :source="record" />
            </template>
            <template slot="f14" slot-scope="text, record">
                <StockSign :code="record.f12" />{{record.f14}}
                <!-- <span class="fs12">{{record.f12}}</span> -->
            </template>
            <template slot="f100" slot-scope="text, record">
                {{record.f100}}
            </template>
            <template slot="f102" slot-scope="text, record">
                {{record.f102}}
            </template>
            <template slot="f103" slot-scope="text, record">
                {{record.f103}}
            </template>
            <template slot="f2" slot-scope="text, record">
                <span v-html="replaceMethod(record.f3, record.f2, 0, 2,'')" />
            </template>
            <template slot="f3" slot-scope="text, record">
                <span v-html="replaceMethod(record.f3, record.f3, 0, 2,'%')" />
            </template>
            <template slot="f6" slot-scope="text, record">
                <span>{{record.f6 | numFilter(8, 2, '亿')}}</span>
            </template>
            <template slot="f7" slot-scope="text, record">
                <span>{{record.f7}}</span>
            </template>
            <template slot="f127" slot-scope="text, record">
                <span v-html="replaceMethod(record.f127, record.f127, 0, 2,'%')" />
            </template>
                <template slot="f149" slot-scope="text, record">
                <span v-html="replaceMethod(record.f149, record.f149, 0, 2,'%')" />
            </template>
            <template slot="f22" slot-scope="text, record">
                <span v-html="replaceMethod(record.f22, record.f22, 0, 2, '%')" />
            </template>
            <template slot="f24" slot-scope="text, record">
                <span v-html="replaceMethod(record.f23, record.f23, 0, 2, '%')" />
            </template>
            <template slot="f25" slot-scope="text, record">
                <span v-html="replaceMethod(record.f25, record.f25, 0, 2, '%')" />
            </template>
            <template slot="zzb_one" slot-scope="text, record">
                <span v-html="replaceMethod(record.zzb_one, record.zzb_one, 0, 2, '‰')" />
            </template>
            <template slot="f20" slot-scope="text, record">
                <span>{{record.f20 | numFilter(8, 2, '亿') }}</span>
            </template>
            <template slot="limit" slot-scope="text, record">
                <span>{{record.stock.limit}}连板</span>
            </template>
            <template slot="keyword" slot-scope="text, record">
                <span>{{record.stock.keyword}}</span>
            </template>
            <template slot="shortDesc" slot-scope="text, record">
                <span>{{record.stock.shortDesc}}</span>
            </template>
            <template slot="desc2" slot-scope="text, record">
                <span>{{record.stock.desc2}}</span>
            </template>
            <template slot="decisionPercent" slot-scope="text, record">
                <span v-html="replaceMethod(record.stock.decisionPercent, record.stock.decisionPercent*100, 0, 2,'%')" />
            </template>
            <template slot="ret100" slot-scope="text, record">
                <span v-html="replaceMethod(record.stock.ret100, record.stock.ret100*100, 0, 2,'%')" />
            </template>
            <template slot="tag" slot-scope="text, record">
                <span v-if="record.stock.tagStocks">
                    <a-tag color="blue" v-for="(t, i) in record.stock.tagStocks" :key="i">
                        <span v-if="t.tag">{{t.tag.name}}</span>
                    </a-tag>
                </span>
                <span v-else>-</span>
            </template>
            <template slot="tags" slot-scope="text, record">
                <span v-if="record.stock.tags">
                    <a-tag color="red" v-for="(t, i) in record.stock.tags.split(',')" :key="i">
                        {{t}}
                    </a-tag>
                </span>
                <span v-else>-</span>
            </template>
            <template slot="finance" slot-scope="text, record">
                <a-row class="fs12">
                    <a-col :span="12">
                        总营收 {{ record.f40 | numFilter(8, 2, '亿')}}
                    </a-col>
                    <a-col :span="12">
                        同比 <span v-html="replaceMethod(record.f41, record.f41, 0, 2, '%')" />
                    </a-col>
                    <a-col :span="12">
                        净利润 {{ record.f45 | numFilter(8, 2, '亿')}}
                    </a-col>
                    <a-col :span="12">
                        同比 <span v-html="replaceMethod(record.f46, record.f46, 0, 2, '%')" />
                    </a-col>
                </a-row>
            </template>
            <template slot="change" slot-scope="text, record">
                <a-row class="fs12">
                    <a-col :span="6">
                        3日 <span v-html="replaceMethod(record.f127, record.f127, 0, 2, '%')" />
                    </a-col>
                    <a-col :span="6">
                        阶段 <span v-html="replaceMethod(record.stock.decisionPercent, record.stock.decisionPercent*100, 0, 2,'%')" />
                    </a-col>
                    <a-col :span="6">
                        底部 <span v-html="replaceMethod(record.stock.ret100, record.stock.ret100*100, 0, 2,'%')" />
                    </a-col>
                    <a-col :span="6">
                        今年<span v-html="replaceMethod(record.f25, record.f25, 0, 2, '%')" />
                    </a-col>
                </a-row>
            </template>
        </a-table>
        <div v-else>
            <a-table 
                v-if="!extra"
                :bordered="bordered"
                :loading="loading"
                :pagination="pn"
                :scroll="{ x: width, y: height }"
                :customRow="customRow"
                :columns="getColumns"
                :rowKey="record => record.code"
                :dataSource="dataSource"
                @change="handleTableChange">
                <template slot="displayName" slot-scope="text, record">
                    <!-- <span :class="{'line': record.black}"> -->
                        <!-- <StockSign :code="record.code" />{{record.source.displayName}} -->
                        <!-- <span class="fs12">{{record.source.code}}</span> -->
                    <!-- </span> -->
                    <StockBar :stock="record" :source="record.source" />
                </template>
                <template slot="f14" slot-scope="text, record">
                    <StockSign :code="record.code" />{{record.source.f14}}
                    <!-- <span class="fs12">{{record.source.f12}}</span> -->
                </template>
                <template slot="reason" slot-scope="text, record">
                    {{record.reason}}
                </template>
                <template slot="date" slot-scope="text, record">
                    {{record.date}}
                </template>
                <template slot="rate" slot-scope="text, record">
                    <span v-html="replaceMethod(record.rate, record.rate*100, 0, 2, '%')" />
                </template>
                <template slot="f100" slot-scope="text, record">
                    {{record.source.f100}}
                </template>
                <template slot="f102" slot-scope="text, record">
                    {{record.source.f102}}
                </template>
                <template slot="f103" slot-scope="text, record">
                    {{record.source.f103}}
                </template>
                <template slot="trend" slot-scope="text, record">
                    {{record.trend}}
                </template>
                <template slot="star" slot-scope="text, record">
                    <a-rate v-model="record.rate" disabled />
                </template>
                <template slot="limit" slot-scope="text, record">
                    <span>{{record.limit}}连板</span>
                </template>
                <template slot="keyword" slot-scope="text, record">
                    <span>{{record.keyword}}</span>
                </template>
                <template slot="shortDesc" slot-scope="text, record">
                    <span>{{record.shortDesc}}</span>
                </template>
                <template slot="desc2" slot-scope="text, record">
                    <span>{{record.desc2}}</span>
                </template>
                <template slot="decisionPercent" slot-scope="text, record">
                    <span v-html="replaceMethod(record.decisionPercent, record.decisionPercent*100, 0, 2,'%')" />
                </template>
                <template slot="ret100" slot-scope="text, record">
                    <span v-html="replaceMethod(record.ret100, record.ret100*100, 0, 2,'%')" />
                </template>
                <template slot="kline" slot-scope="text, record">
                    <a-popover @visibleChange="(v)=> {visibleChange(v, item)}" title="K线解释" placement="right" v-for="(item, key) in record.kline.split(',')" :key="key">
                        <template slot="content">
                        <div v-if="selected" class="kline-content">
                                <p>名字：{{selected.name}}</p>
                                <p>描述：{{selected.desc}}</p>
                                <p>胜率：{{selected.rate}}</p>
                        </div>
                        </template>
                        <span class="mr4" :class="{'red': isRedKline(item)}">{{item}}</span>
                    </a-popover>
                </template>
                <template slot="tag" slot-scope="text, record">
                    <span v-if="record.tagStocks">
                        <a-tag color="blue" v-for="(t, i) in record.tagStocks" :key="i">
                            <span v-if="t.tag">
                                {{t.tag.name}}
                            </span>
                        </a-tag>
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="tags" slot-scope="text, record">
                    <span v-if="record.tags">
                        <a-tag color="red" v-for="(t, i) in record.tags.split(',')" :key="i">
                            {{t}}
                        </a-tag>
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="sort" slot-scope="text, record">
                    <span v-if="record.source.stock && record.source.stock.sortFir && record.source.stock.sortSec">
                        {{record.source.stock.sortFir.name + '-' + record.source.stock.sortSec.name}}
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="f2" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f3, record.source.f2, 0, 2, '')" />
                </template>
                <template slot="f3" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f3, record.source.f3, 0, 2, '%')" />
                </template>
                <template slot="percent" slot-scope="text, record">
                    <span v-html="replaceMethod(record.percent, record.percent * 100, 0, 2, '%')" />
                </template>
                <template slot="ret" slot-scope="text, record">
                    <span v-html="replaceMethod(record.ret, record.ret * 100, 0, 2, '%')" />
                </template>
                <template slot="md" slot-scope="text, record">
                    <span v-html="replaceMethod(record.md, record.md * 100, 0, 2, '%')" />
                </template>
                <template slot="alpha" slot-scope="text, record">
                    <span v-html="replaceMethod(record.alpha, record.alpha * 100, 0, 2, '%')" />
                </template>
                <template slot="yoypni" slot-scope="text, record">
                    <span v-html="replaceMethod(record.yoypni, record.yoypni * 100, 0, 2, '%')" />
                </template>
                <template slot="f6" slot-scope="text, record">
                    <span>{{record.source.f6 | numFilter(8, 2, '亿')}}</span>
                </template>
                <template slot="f7" slot-scope="text, record">
                    <span>{{record.source.f7}}</span>
                </template>
                <template slot="f127" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f127, record.source.f127, 0, 2,'%')" />
                </template>
                    <template slot="f149" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f149, record.source.f149, 0, 2,'%')" />
                </template>
                <template slot="f22" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f22, record.source.f22, 0, 2, '%')" />
                </template>
                <template slot="f24" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f23, record.source.f23, 0, 2, '%')" />
                </template>
                <template slot="f25" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f25, record.source.f25, 0, 2, '%')" />
                </template>
                <template slot="f20" slot-scope="text, record">
                    <span>{{record.source.f20 | numFilter(8, 2, '亿') }}</span>
                </template>
                <template slot="zzb_one" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.zzb_one, record.source.zzb_one, 0, 2, '‰')" />
                </template>
                <template slot="finance" slot-scope="text, record">
                    <a-row class="fs12"> 
                    <a-col :span="12">
                        总营收 {{ record.source.f40 | numFilter(8, 2, '亿')}}
                    </a-col>
                    <a-col :span="12">
                        同比 <span v-html="replaceMethod(record.source.f41, record.source.f41, 0, 2, '%')" />
                    </a-col>
                    <a-col :span="12">
                        净利润 {{ record.source.f45 | numFilter(8, 2, '亿')}}
                    </a-col>
                    <a-col :span="12">
                        同比 <span v-html="replaceMethod(record.source.f46, record.source.f46, 0, 2, '%')" />
                    </a-col>
                    </a-row>
                </template>
                <template slot="totalscore" slot-scope="text, record">
                {{record.score}} （<span v-html="replaceMethod(record.score - record.lastscore, record.score - record.lastscore, 0, 1,'')" />）
                </template>
                <template slot="score" slot-scope="text, record">
                    <a-row class="text-center"> 
                        <a-col :span="12">
                            基本 {{record.score1}}
                        </a-col>
                        <a-col :span="12">
                            技术 {{record.score2}}
                        </a-col>
                        <a-col :span="12">
                            资金 {{record.score3}}
                        </a-col>
                        <a-col :span="12">
                            风口 {{record.score4}}
                        </a-col>
                    </a-row>
                </template>
                <template slot="change" slot-scope="text, record">
                    <a-row class="fs12">
                        <a-col :span="6">
                            3日 <span v-html="replaceMethod(record.source.f127, record.source.f127, 0, 2, '%')" />
                        </a-col>
                        <a-col :span="6">
                            阶段 <span v-html="replaceMethod(record.decisionPercent, record.decisionPercent*100, 0, 2,'%')" />
                        </a-col>
                        <a-col :span="6">
                            底部 <span v-html="replaceMethod(record.ret100, record.ret100*100, 0, 2,'%')" />
                        </a-col>
                        <a-col :span="6">
                            今年 <span v-html="replaceMethod(record.source.f25, record.source.f25, 0, 2, '%')" />
                        </a-col>
                    </a-row>
                </template>
            </a-table>
            <a-table 
                v-else
                :bordered="bordered"
                :loading="loading"
                :pagination="pn"
                :scroll="{ x: width, y: height }"
                :customRow="customRow"
                :columns="getColumns"
                :rowKey="record => record.code"
                :dataSource="dataSource"
                @change="handleTableChange">
                <template slot="displayName" slot-scope="text, record">
                    <StockBar :stock="record" :source="record.source" />
                </template>
                <template slot="displayName-fixed" slot-scope="text, record">
                    <StockBar :stock="record" :source="record.source" />
                </template>
                <template slot="f14" slot-scope="text, record">
                    <StockSign :code="record.code" />{{record.source.f14}}
                    <!-- <span class="fs12">{{record.source.f12}}</span> -->
                </template>
                <template slot="reason" slot-scope="text, record">
                    {{record.reason}}
                </template>
                <template slot="date" slot-scope="text, record">
                    {{record.date}}
                </template>
                <template slot="rate" slot-scope="text, record">
                    <span v-html="replaceMethod(record.rate, record.rate*100, 0, 2, '%')" />
                </template>
                <template slot="f100" slot-scope="text, record">
                    {{record.source.f100}}
                </template>
                <template slot="f102" slot-scope="text, record">
                    {{record.source.f102}}
                </template>
                <template slot="f103" slot-scope="text, record">
                    {{record.source.f103}}
                </template>
                <template slot="trend" slot-scope="text, record">
                    {{record.trend}}
                </template>
                <template slot="star" slot-scope="text, record">
                    <a-rate v-model="record.rate" disabled />
                </template>
                <template slot="limit" slot-scope="text, record">
                    <span>{{record.limit}}连板</span>
                </template>
                <template slot="keyword" slot-scope="text, record">
                    <span>{{record.keyword}}</span>
                </template>
                <template slot="shortDesc" slot-scope="text, record">
                    <span>{{record.shortDesc}}</span>
                </template>
                <template slot="decisionPercent" slot-scope="text, record">
                    <span v-html="replaceMethod(record.decisionPercent, record.decisionPercent*100, 0, 2,'%')" />
                </template>
                <template slot="ret100" slot-scope="text, record">
                    <span v-html="replaceMethod(record.ret100, record.ret100*100, 0, 2,'%')" />
                </template>
                <template slot="kline" slot-scope="text, record">
                    <a-popover @visibleChange="(v)=> {visibleChange(v, item)}" title="K线解释" placement="right" v-for="(item, key) in record.kline.split(',')" :key="key">
                        <template slot="content">
                        <div v-if="selected" class="kline-content">
                                <p>名字：{{selected.name}}</p>
                                <p>描述：{{selected.desc}}</p>
                                <p>胜率：{{selected.rate}}</p>
                        </div>
                        </template>
                        <span class="mr4" :class="{'red': isRedKline(item)}">{{item}}</span>
                    </a-popover>
                </template>
                <template slot="tag" slot-scope="text, record">
                    <span v-if="record.tagStocks">
                        <a-tag color="blue" v-for="(t, i) in record.tagStocks" :key="i">
                            <span v-if="t.tag">
                                {{t.tag.name}}
                            </span>
                        </a-tag>
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="tags" slot-scope="text, record">
                    <span v-if="record.tags">
                        <a-tag color="red" v-for="(t, i) in record.tags.split(',')" :key="i">
                            {{t}}
                        </a-tag>
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="sort" slot-scope="text, record">
                    <span v-if="record.source.stock && record.source.stock.sortFir && record.source.stock.sortSec">
                        {{record.source.stock.sortFir.name + '-' + record.source.stock.sortSec.name}}
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="f2" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f3, record.source.f2, 0, 2, '')" />
                </template>
                <template slot="f3" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f3, record.source.f3, 0, 2, '%')" />
                </template>
                <template slot="percent" slot-scope="text, record">
                    <span v-html="replaceMethod(record.percent, record.percent * 100, 0, 2, '%')" />
                </template>
                <template slot="ret" slot-scope="text, record">
                    <span v-html="replaceMethod(record.ret, record.ret * 100, 0, 2, '%')" />
                </template>
                <template slot="md" slot-scope="text, record">
                    <span v-html="replaceMethod(record.md, record.md * 100, 0, 2, '%')" />
                </template>
                <template slot="alpha" slot-scope="text, record">
                    <span v-html="replaceMethod(record.alpha, record.alpha * 100, 0, 2, '%')" />
                </template>
                <template slot="yoypni" slot-scope="text, record">
                    <span v-html="replaceMethod(record.yoypni, record.yoypni * 100, 0, 2, '%')" />
                </template>
                <template slot="f6" slot-scope="text, record">
                    <span>{{record.source.f6 | numFilter(8, 2, '亿')}}</span>
                </template>
                <template slot="f7" slot-scope="text, record">
                    <span>{{record.source.f7}}</span>
                </template>
                <template slot="f127" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f127, record.source.f127, 0, 2,'%')" />
                </template>
                    <template slot="f149" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f149, record.source.f149, 0, 2,'%')" />
                </template>
                <template slot="f22" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f22, record.source.f22, 0, 2, '%')" />
                </template>
                <template slot="f24" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f23, record.source.f23, 0, 2, '%')" />
                </template>
                <template slot="f25" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.f25, record.source.f25, 0, 2, '%')" />
                </template>
                <template slot="f20" slot-scope="text, record">
                    <span>{{record.source.f20 | numFilter(8, 2, '亿') }}</span>
                </template>
                <template slot="zzb_one" slot-scope="text, record">
                    <span v-html="replaceMethod(record.source.zzb_one, record.source.zzb_one, 0, 2, '‰')" />
                </template>
                <template slot="change" slot-scope="text, record">
                    <a-row class="fs12">
                        <a-col :span="6">
                            3日 <span v-html="replaceMethod(record.source.f127, record.source.f127, 0, 2, '%')" />
                        </a-col>
                        <a-col :span="6">
                            阶段 <span v-html="replaceMethod(record.decisionPercent, record.decisionPercent*100, 0, 2,'%')" />
                        </a-col>
                        <a-col :span="6">
                            底部 <span v-html="replaceMethod(record.ret100, record.ret100*100, 0, 2,'%')" />
                        </a-col>
                        <a-col :span="6">
                            今年 <span v-html="replaceMethod(record.source.f25, record.source.f25, 0, 2, '%')" />
                        </a-col>
                    </a-row>
                </template>
                <template slot="finance" slot-scope="text, record">
                    <a-row class="fs12"> 
                    <a-col :span="12">
                        总营收 {{ record.source.f40 | numFilter(8, 2, '亿')}}
                    </a-col>
                    <a-col :span="12">
                        同比 <span v-html="replaceMethod(record.source.f41, record.source.f41, 0, 2, '%')" />
                    </a-col>
                    <a-col :span="12">
                        净利润 {{ record.source.f45 | numFilter(8, 2, '亿')}}
                    </a-col>
                    <a-col :span="12">
                        同比 <span v-html="replaceMethod(record.source.f46, record.source.f46, 0, 2, '%')" />
                    </a-col>
                    </a-row>
                </template>
                <template slot="totalscore" slot-scope="text, record">
                    {{record.score}} （<span v-html="replaceMethod(record.score - record.lastscore, record.score - record.lastscore, 0, 1,'')" />）
                </template>
                <template slot="score" slot-scope="text, record">
                    <a-row class="text-center"> 
                        <a-col :span="12">
                            基本 {{record.score1}}
                        </a-col>
                        <a-col :span="12">
                            技术 {{record.score2}}
                        </a-col>
                        <a-col :span="12">
                            资金 {{record.score3}}
                        </a-col>
                        <a-col :span="12">
                            风口 {{record.score4}}
                        </a-col>
                    </a-row>
                </template>
                <template slot="change" slot-scope="text, record">
                    <a-row class="fs12">
                        <a-col :span="6">
                            3日 <span v-html="replaceMethod(record.source.f127, record.source.f127, 0, 2, '%')" />
                        </a-col>
                        <a-col :span="6">
                            阶段 <span v-html="replaceMethod(record.decisionPercent, record.decisionPercent*100, 0, 2,'%')" />
                        </a-col>
                        <a-col :span="6">
                            底部 <span v-html="replaceMethod(record.ret100, record.ret100*100, 0, 2,'%')" />
                        </a-col>
                        <a-col :span="6">
                            今年 <span v-html="replaceMethod(record.source.f25, record.source.f25, 0, 2, '%')" />
                        </a-col>
                    </a-row>
                </template>
                <template slot="expandedRowRender" slot-scope="record">
                    <a-table :columns="extraColumns" :dataSource="[record]" :pagination="false">
                        <template slot="change" slot-scope="text, inrecord">
                            <a-row class="fs12">
                                <a-col :span="6">
                                    3日 <span v-html="replaceMethod(inrecord.source.f127, inrecord.source.f127, 0, 2, '%')" />
                                </a-col>
                                <a-col :span="6">
                                    阶段 <span v-html="replaceMethod(inrecord.decisionPercent, inrecord.decisionPercent*100, 0, 2,'%')" />
                                </a-col>
                                <a-col :span="6">
                                    底部 <span v-html="replaceMethod(inrecord.ret100, inrecord.ret100*100, 0, 2,'%')" />
                                </a-col>
                                <a-col :span="6">
                                    今年 <span v-html="replaceMethod(inrecord.source.f25, inrecord.source.f25, 0, 2, '%')" />
                                </a-col>
                            </a-row>
                        </template>
                        <template slot="finance" slot-scope="text, inrecord">
                            <a-row class="fs12"> 
                                <a-col :span="12">
                                    <span class="mr4">总营收 {{ inrecord.source.f40 | numFilter(8, 2, '亿')}}</span>
                                    同比 <span v-html="replaceMethod(inrecord.source.f41, inrecord.source.f41, 0, 2, '%')" />
                                </a-col>
                                <a-col :span="12">
                                    <span class="mr4">净利润 {{ inrecord.source.f45 | numFilter(8, 2, '亿')}}</span>
                                    同比 <span v-html="replaceMethod(inrecord.source.f46, inrecord.source.f46, 0, 2, '%')" />
                                </a-col>
                            </a-row>
                        </template>
                        <template slot="alltags" slot-scope="text, inrecord">
                            <StockTag :stock=inrecord fold=true />
                        </template>
                    </a-table>
                </template>
            </a-table>
        </div>
    </div>
</template>

<script>
// stock -> source

// 三类table, todo 两类table合并solt
import { mapActions } from 'vuex';
import TABLE from 'constants/table'
import StockSign from 'components/StockSign'
import StockBar from 'components/StockBar'
import StockTag from 'components/StockTag'

export default {
    data() {
        return {
            width: 0,
            loading: false,
            pn: false,
            selected: null,
            extraColumns: TABLE.TABLE_EXTRA_COLUMNS,
            klines: []
        }
    },
    components: {
        StockSign,
        StockBar,
        StockTag
    },
    props: {
        dataSource: Array,
        columns: Array,
        extra: Boolean,
        source: Boolean,
        height: Number,
        pagination: [Object, Boolean],
        sorter: Boolean,
        type: String,
        bordered: Boolean,
    },
    computed: {
        getColumns() {
            let list = [];
            this.width = 0;
            this.columns.forEach(element => {
                const item = TABLE.TABLE_COLUMNS.find((i) => {
                    return i.dataIndex === element;
                });

                if (item) {
                    item['sorter'] = this.sorter;
                    list.push(item);
                    this.width = this.width + item.width;
                }
            });
            return list;
        }
    },
    created() {},
    watch: {
        pagination: {
            immediate: true,
            handler(val) {
                this.pn = val;
            }
        }
    },
    mounted() {
        this.getKline();
    },
    methods: {
        ...mapActions(('stock'),['showStockDetail']),
        isRedKline(code) {
            if (this.klines.indexOf(code) > -1) {
                return true
            } 

            return false;
        },
        getKline() {
            this.klines = []
            this.$api.black.getKline().then((result) => {
                result.forEach(element => {
                    this.klines.push(element.func)
                });
            }).catch((err) => {
                
            }).finally(() => {
            });
        },
        visibleChange(visible, id) {
            if (visible) {
                this.$api.black.showKline(id).then((result) => {
                    this.selected = result.kline
                }).catch((err) => {
                    
                }).finally(() => {
                });
            }
        },
        customRow(record, index) {
            return {
                on: {
                    click: () => {
                        if (this.type === 'list') {
                            this.$emit('stockSelect', record.code);
                            return;
                        }
                        this.showStockDetail(record.code || record.f12);
                    },
                    mouseenter: () => {
                        // this.type === 'mouseenter' && this.$emit('stockSelect', record.code);
                    },
                },
            }
        },
        handleTableChange (pagination, filters, sorter) {
            this.$emit("change", pagination, filters, sorter);
        }
    },
}
</script>

<style scoped lang="less">
.kline-content {
    width: 300px;
}
</style>